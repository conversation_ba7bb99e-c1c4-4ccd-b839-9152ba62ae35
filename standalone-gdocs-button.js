/**
 * Standalone Google Docs Export Button for Stickara
 * 
 * This is a standalone script that can be included directly in the dashboard page
 * to add Google Docs export functionality.
 */

(function() {
    console.log("Stickara: Standalone Google Docs Export Button loaded");
    
    // Configuration
    const config = {
        buttonId: 'stickara-standalone-gdocs-btn',
        buttonText: 'Export to Google Docs',
        buttonIcon: '📄',
        buttonColor: '#4285F4',
        buttonHoverColor: '#3367D6',
        checkInterval: 500 // Check every 500ms
    };
    
    // DOM Elements
    let exportButton = null;
    let exportModal = null;
    let dashboardContainer = null;
    
    /**
     * Main initialization function
     */
    function init() {
        console.log("Stickara: Initializing standalone Google Docs button");
        
        // Find dashboard container
        dashboardContainer = document.querySelector('.stickara-dashboard-container, .dashboard-container');
        
        if (dashboardContainer) {
            console.log("Stickara: Dashboard container found");
            createExportButton();
            setupEventListeners();
        } else {
            console.log("Stickara: Dashboard container not found, will check periodically");
            // Start checking for dashboard
            setInterval(() => {
                dashboardContainer = document.querySelector('.stickara-dashboard-container, .dashboard-container');
                if (dashboardContainer && !document.getElementById(config.buttonId)) {
                    console.log("Stickara: Dashboard container found during periodic check");
                    createExportButton();
                    setupEventListeners();
                }
            }, config.checkInterval);
        }
    }
    
    /**
     * Creates the export button
     */
    function createExportButton() {
        // Check if button already exists
        if (document.getElementById(config.buttonId)) {
            return;
        }
        
        // Find or create container for button
        const actionsContainer = findOrCreateActionsContainer();
        
        // Create button
        exportButton = document.createElement('button');
        exportButton.id = config.buttonId;
        exportButton.innerHTML = `<span style="margin-right:5px;">${config.buttonIcon}</span>${config.buttonText}`;
        exportButton.style.backgroundColor = config.buttonColor;
        exportButton.style.color = 'white';
        exportButton.style.border = 'none';
        exportButton.style.borderRadius = '4px';
        exportButton.style.padding = '8px 12px';
        exportButton.style.margin = '5px';
        exportButton.style.cursor = 'pointer';
        exportButton.style.fontWeight = 'bold';
        exportButton.style.fontSize = '14px';
        exportButton.style.display = 'none'; // Hidden by default
        exportButton.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.2)';
        exportButton.style.transition = 'all 0.2s ease';
        
        // Add hover effect
        exportButton.addEventListener('mouseover', function() {
            this.style.backgroundColor = config.buttonHoverColor;
            this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
        });
        
        exportButton.addEventListener('mouseout', function() {
            this.style.backgroundColor = config.buttonColor;
            this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.2)';
        });
        
        // Add to container
        actionsContainer.appendChild(exportButton);
        
        console.log("Stickara: Export button created");
    }
    
    /**
     * Finds or creates a container for the button
     * @returns {HTMLElement} The container
     */
    function findOrCreateActionsContainer() {
        // Try to find existing container
        let container = document.querySelector('.stickara-dashboard-actions, .dashboard-actions');
        
        if (!container) {
            // Create new container
            container = document.createElement('div');
            container.className = 'stickara-dashboard-actions';
            container.style.padding = '10px';
            container.style.display = 'flex';
            container.style.justifyContent = 'flex-end';
            container.style.borderBottom = '1px solid #eee';
            
            // Add to dashboard
            dashboardContainer.insertBefore(container, dashboardContainer.firstChild);
            
            console.log("Stickara: Created actions container");
        }
        
        return container;
    }
    
    /**
     * Sets up event listeners
     */
    function setupEventListeners() {
        // Listen for checkbox changes
        document.addEventListener('change', function(event) {
            if (event.target.type === 'checkbox') {
                setTimeout(updateButtonVisibility, 100);
            }
        });
        
        // Listen for export button click
        if (exportButton) {
            exportButton.addEventListener('click', handleExportButtonClick);
        }
        
        // Periodically check for selected items
        setInterval(updateButtonVisibility, 1000);
    }
    
    /**
     * Updates the visibility of the export button based on selected items
     */
    function updateButtonVisibility() {
        if (!exportButton) return;
        
        const selectedItems = getSelectedItems();
        
        if (selectedItems.length > 0) {
            exportButton.style.display = 'inline-block';
        } else {
            exportButton.style.display = 'none';
        }
    }
    
    /**
     * Gets selected items from the dashboard
     * @returns {Array} Selected items
     */
    function getSelectedItems() {
        const checkedCheckboxes = document.querySelectorAll('.stickara-item-checkbox:checked, .stickara-dashboard-item input[type="checkbox"]:checked, .dashboard-item input[type="checkbox"]:checked');
        
        return Array.from(checkedCheckboxes).map(checkbox => {
            const itemElement = checkbox.closest('.stickara-dashboard-item, .dashboard-item');
            if (!itemElement) return null;
            
            return {
                id: itemElement.dataset.itemId || '',
                type: itemElement.dataset.itemType || 'unknown',
                url: itemElement.dataset.itemUrl || '',
                text: itemElement.querySelector('.stickara-item-text, .item-content')?.textContent || ''
            };
        }).filter(Boolean);
    }
    
    /**
     * Handles export button click
     */
    function handleExportButtonClick() {
        console.log("Stickara: Export button clicked");
        
        // Show export modal
        showExportModal();
    }
    
    /**
     * Shows the export modal
     */
    function showExportModal() {
        // If modal doesn't exist, create it
        if (!exportModal) {
            createExportModal();
        }
        
        // Set default title
        const titleInput = document.getElementById('standalone-gdocs-title');
        if (titleInput) {
            titleInput.value = `Stickara Export - ${new Date().toLocaleDateString()}`;
        }
        
        // Show modal
        exportModal.style.display = 'flex';
    }
    
    /**
     * Creates the export modal
     */
    function createExportModal() {
        // Create modal container
        exportModal = document.createElement('div');
        exportModal.id = 'standalone-gdocs-modal';
        exportModal.style.position = 'fixed';
        exportModal.style.top = '0';
        exportModal.style.left = '0';
        exportModal.style.width = '100%';
        exportModal.style.height = '100%';
        exportModal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        exportModal.style.display = 'none';
        exportModal.style.justifyContent = 'center';
        exportModal.style.alignItems = 'center';
        exportModal.style.zIndex = '10000';
        
        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.style.backgroundColor = 'white';
        modalContent.style.padding = '20px';
        modalContent.style.borderRadius = '8px';
        modalContent.style.maxWidth = '400px';
        modalContent.style.width = '90%';
        modalContent.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
        
        // Create modal header
        const modalHeader = document.createElement('div');
        modalHeader.style.display = 'flex';
        modalHeader.style.justifyContent = 'space-between';
        modalHeader.style.alignItems = 'center';
        modalHeader.style.marginBottom = '15px';
        
        const modalTitle = document.createElement('h3');
        modalTitle.textContent = 'Export to Google Docs';
        modalTitle.style.margin = '0';
        modalTitle.style.color = '#4285F4';
        
        const closeButton = document.createElement('button');
        closeButton.innerHTML = '&times;';
        closeButton.style.background = 'none';
        closeButton.style.border = 'none';
        closeButton.style.fontSize = '24px';
        closeButton.style.cursor = 'pointer';
        closeButton.style.color = '#666';
        closeButton.style.padding = '0';
        
        modalHeader.appendChild(modalTitle);
        modalHeader.appendChild(closeButton);
        
        // Create modal body
        const modalBody = document.createElement('div');
        
        // Create title input
        const titleLabel = document.createElement('label');
        titleLabel.textContent = 'Document Title:';
        titleLabel.style.display = 'block';
        titleLabel.style.marginBottom = '5px';
        titleLabel.style.fontWeight = 'bold';
        
        const titleInput = document.createElement('input');
        titleInput.type = 'text';
        titleInput.id = 'standalone-gdocs-title';
        titleInput.placeholder = 'Enter document title';
        titleInput.style.width = '100%';
        titleInput.style.padding = '8px';
        titleInput.style.marginBottom = '15px';
        titleInput.style.border = '1px solid #ccc';
        titleInput.style.borderRadius = '4px';
        
        // Create options
        const optionsLabel = document.createElement('label');
        optionsLabel.textContent = 'Export Options:';
        optionsLabel.style.display = 'block';
        optionsLabel.style.marginBottom = '5px';
        optionsLabel.style.fontWeight = 'bold';
        
        // Create URL option
        const urlOption = document.createElement('div');
        urlOption.style.marginBottom = '10px';
        
        const urlCheckbox = document.createElement('input');
        urlCheckbox.type = 'checkbox';
        urlCheckbox.id = 'standalone-gdocs-include-url';
        urlCheckbox.checked = true;
        urlCheckbox.style.marginRight = '5px';
        
        const urlLabel = document.createElement('label');
        urlLabel.htmlFor = 'standalone-gdocs-include-url';
        urlLabel.textContent = 'Include source URLs';
        
        urlOption.appendChild(urlCheckbox);
        urlOption.appendChild(urlLabel);
        
        // Create tags option
        const tagsOption = document.createElement('div');
        tagsOption.style.marginBottom = '10px';
        
        const tagsCheckbox = document.createElement('input');
        tagsCheckbox.type = 'checkbox';
        tagsCheckbox.id = 'standalone-gdocs-include-tags';
        tagsCheckbox.checked = true;
        tagsCheckbox.style.marginRight = '5px';
        
        const tagsLabel = document.createElement('label');
        tagsLabel.htmlFor = 'standalone-gdocs-include-tags';
        tagsLabel.textContent = 'Include tags';
        
        tagsOption.appendChild(tagsCheckbox);
        tagsOption.appendChild(tagsLabel);
        
        // Create status message
        const statusMessage = document.createElement('div');
        statusMessage.id = 'standalone-gdocs-status';
        statusMessage.style.marginTop = '10px';
        statusMessage.style.padding = '8px';
        statusMessage.style.borderRadius = '4px';
        statusMessage.style.display = 'none';
        
        // Add elements to modal body
        modalBody.appendChild(titleLabel);
        modalBody.appendChild(titleInput);
        modalBody.appendChild(optionsLabel);
        modalBody.appendChild(urlOption);
        modalBody.appendChild(tagsOption);
        modalBody.appendChild(statusMessage);
        
        // Create modal footer
        const modalFooter = document.createElement('div');
        modalFooter.style.display = 'flex';
        modalFooter.style.justifyContent = 'flex-end';
        modalFooter.style.marginTop = '20px';
        
        const cancelButton = document.createElement('button');
        cancelButton.textContent = 'Cancel';
        cancelButton.style.padding = '8px 16px';
        cancelButton.style.marginRight = '10px';
        cancelButton.style.backgroundColor = '#f1f1f1';
        cancelButton.style.color = '#333';
        cancelButton.style.border = 'none';
        cancelButton.style.borderRadius = '4px';
        cancelButton.style.cursor = 'pointer';
        
        const exportConfirmButton = document.createElement('button');
        exportConfirmButton.textContent = 'Export';
        exportConfirmButton.id = 'standalone-gdocs-export-confirm';
        exportConfirmButton.style.padding = '8px 16px';
        exportConfirmButton.style.backgroundColor = '#4285F4';
        exportConfirmButton.style.color = 'white';
        exportConfirmButton.style.border = 'none';
        exportConfirmButton.style.borderRadius = '4px';
        exportConfirmButton.style.cursor = 'pointer';
        exportConfirmButton.style.fontWeight = 'bold';
        
        modalFooter.appendChild(cancelButton);
        modalFooter.appendChild(exportConfirmButton);
        
        // Assemble modal
        modalContent.appendChild(modalHeader);
        modalContent.appendChild(modalBody);
        modalContent.appendChild(modalFooter);
        exportModal.appendChild(modalContent);
        
        // Add to body
        document.body.appendChild(exportModal);
        
        // Add event listeners
        closeButton.addEventListener('click', () => {
            exportModal.style.display = 'none';
        });
        
        cancelButton.addEventListener('click', () => {
            exportModal.style.display = 'none';
        });
        
        exportConfirmButton.addEventListener('click', () => {
            handleExportConfirm();
        });
        
        // Close modal when clicking outside
        exportModal.addEventListener('click', (event) => {
            if (event.target === exportModal) {
                exportModal.style.display = 'none';
            }
        });
        
        console.log("Stickara: Export modal created");
    }
    
    /**
     * Handles export confirmation
     */
    function handleExportConfirm() {
        const statusMessage = document.getElementById('standalone-gdocs-status');
        if (!statusMessage) return;
        
        // Show message
        statusMessage.textContent = 'Exporting to Google Docs...';
        statusMessage.style.display = 'block';
        statusMessage.style.backgroundColor = '#cce5ff';
        statusMessage.style.color = '#004085';
        statusMessage.style.border = '1px solid #b8daff';
        
        // Simulate export process
        setTimeout(() => {
            // Check if Chrome extensions API is available
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
                // Try to use Stickara's API
                chrome.runtime.sendMessage({
                    action: 'exportToGoogleDocs',
                    items: getSelectedItems(),
                    options: {
                        title: document.getElementById('standalone-gdocs-title').value,
                        includeUrl: document.getElementById('standalone-gdocs-include-url').checked,
                        includeTags: document.getElementById('standalone-gdocs-include-tags').checked
                    }
                }, function(response) {
                    if (response && response.success) {
                        statusMessage.innerHTML = `Export successful! <a href="${response.documentUrl}" target="_blank" style="color:inherit;font-weight:bold;">Open Document</a>`;
                        statusMessage.style.backgroundColor = '#d4edda';
                        statusMessage.style.color = '#155724';
                        statusMessage.style.border = '1px solid #c3e6cb';
                    } else {
                        statusMessage.textContent = 'Export failed. Please try again later.';
                        statusMessage.style.backgroundColor = '#f8d7da';
                        statusMessage.style.color = '#721c24';
                        statusMessage.style.border = '1px solid #f5c6cb';
                    }
                });
            } else {
                // Show message for standalone version
                statusMessage.textContent = 'This feature is coming soon! The Google Docs export functionality is currently being implemented.';
                statusMessage.style.backgroundColor = '#fff3cd';
                statusMessage.style.color = '#856404';
                statusMessage.style.border = '1px solid #ffeeba';
            }
        }, 1000);
    }
    
    // Initialize when the script loads
    init();
})();

console.log("Stickara: Standalone Google Docs Export Button Initialized");
