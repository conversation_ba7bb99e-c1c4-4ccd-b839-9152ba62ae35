/**
 * Direct Google Docs Button Injector for Stickara
 * 
 * This script directly injects the Google Docs export button into the dashboard
 * with a more aggressive approach to ensure it appears when needed.
 */

(function() {
    // Configuration
    const config = {
        buttonId: 'stickara-direct-gdocs-btn',
        buttonText: 'Export to Google Docs',
        buttonIcon: '📄',
        buttonColor: '#4285F4',
        buttonHoverColor: '#3367D6',
        checkInterval: 1000, // Check for dashboard and selected items every 1 second
        maxChecks: 30 // Maximum number of checks before giving up
    };
    
    // State
    let checkCount = 0;
    let checkIntervalId = null;
    let button = null;
    
    /**
     * Main initialization function
     */
    function init() {
        console.log("Stickara Direct GDocs: Initializing");
        
        // Start checking for dashboard
        startChecking();
        
        // Add global event listeners
        addGlobalEventListeners();
    }
    
    /**
     * Starts checking for the dashboard and selected items
     */
    function startChecking() {
        // Clear any existing interval
        if (checkIntervalId) {
            clearInterval(checkIntervalId);
        }
        
        checkCount = 0;
        
        // Set up interval to check for dashboard and selected items
        checkIntervalId = setInterval(() => {
            checkCount++;
            
            // Check if we should stop checking
            if (checkCount > config.maxChecks) {
                clearInterval(checkIntervalId);
                console.log("Stickara Direct GDocs: Stopped checking after maximum attempts");
                return;
            }
            
            // Try to find the dashboard
            const dashboardContainer = document.querySelector('.stickara-dashboard-container, .dashboard-container');
            if (dashboardContainer) {
                // Try to inject the button
                injectButton();
                
                // Check for selected items
                updateButtonVisibility();
            }
        }, config.checkInterval);
    }
    
    /**
     * Adds global event listeners
     */
    function addGlobalEventListeners() {
        // Listen for changes to checkboxes
        document.addEventListener('change', function(event) {
            // Check if the changed element is a checkbox
            if (event.target.type === 'checkbox') {
                // Wait a moment for any dashboard logic to complete
                setTimeout(updateButtonVisibility, 100);
            }
        });
        
        // Listen for clicks on the button
        document.addEventListener('click', function(event) {
            if (event.target.id === config.buttonId || event.target.closest('#' + config.buttonId)) {
                handleButtonClick();
            }
        });
        
        // Listen for potential dashboard events
        document.addEventListener('stickaraDashboardLoaded', function() {
            console.log("Stickara Direct GDocs: Dashboard loaded event detected");
            injectButton();
        });
        
        document.addEventListener('stickaraSelectionChanged', function() {
            console.log("Stickara Direct GDocs: Selection changed event detected");
            updateButtonVisibility();
        });
    }
    
    /**
     * Injects the export button into the dashboard
     */
    function injectButton() {
        // Check if button already exists
        if (document.getElementById(config.buttonId)) {
            button = document.getElementById(config.buttonId);
            return;
        }
        
        console.log("Stickara Direct GDocs: Injecting button");
        
        // Find or create the container for the button
        let container = findOrCreateButtonContainer();
        if (!container) {
            console.warn("Stickara Direct GDocs: Could not find or create button container");
            return;
        }
        
        // Create the button
        button = document.createElement('button');
        button.id = config.buttonId;
        button.className = 'stickara-action-button stickara-gdocs-button';
        button.title = 'Export selected items to Google Docs';
        button.innerHTML = `<span class="stickara-icon">${config.buttonIcon}</span> ${config.buttonText}`;
        
        // Style the button
        button.style.backgroundColor = config.buttonColor;
        button.style.color = 'white';
        button.style.border = 'none';
        button.style.borderRadius = '4px';
        button.style.padding = '8px 12px';
        button.style.margin = '5px';
        button.style.cursor = 'pointer';
        button.style.fontWeight = 'bold';
        button.style.fontSize = '14px';
        button.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.2)';
        button.style.transition = 'all 0.2s ease';
        button.style.display = 'none'; // Hidden by default
        
        // Add hover effect
        button.addEventListener('mouseover', function() {
            this.style.backgroundColor = config.buttonHoverColor;
            this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
        });
        
        button.addEventListener('mouseout', function() {
            this.style.backgroundColor = config.buttonColor;
            this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.2)';
        });
        
        // Add to container
        container.appendChild(button);
        
        // Check if there are already selected items
        updateButtonVisibility();
    }
    
    /**
     * Finds or creates a container for the button
     * @returns {HTMLElement} The container element
     */
    function findOrCreateButtonContainer() {
        // Try various container selectors
        const containerSelectors = [
            '.stickara-dashboard-actions',
            '.dashboard-actions',
            '.stickara-dashboard-toolbar',
            '.dashboard-toolbar',
            '.stickara-dashboard-header',
            '.dashboard-header'
        ];
        
        // Try to find an existing container
        for (const selector of containerSelectors) {
            const container = document.querySelector(selector);
            if (container) {
                return container;
            }
        }
        
        // If no container found, create one
        const dashboardContainer = document.querySelector('.stickara-dashboard-container, .dashboard-container');
        if (!dashboardContainer) {
            return null;
        }
        
        // Create a new container
        const container = document.createElement('div');
        container.className = 'stickara-dashboard-actions';
        container.style.padding = '10px';
        container.style.display = 'flex';
        container.style.justifyContent = 'flex-end';
        container.style.borderBottom = '1px solid #eee';
        
        // Add to dashboard container
        dashboardContainer.insertBefore(container, dashboardContainer.firstChild);
        
        return container;
    }
    
    /**
     * Updates the visibility of the button based on selected items
     */
    function updateButtonVisibility() {
        if (!button) {
            injectButton();
            if (!button) return;
        }
        
        // Check for selected items
        const selectedItems = getSelectedItems();
        
        if (selectedItems.length > 0) {
            console.log(`Stickara Direct GDocs: Found ${selectedItems.length} selected items, showing button`);
            button.style.display = 'inline-block';
        } else {
            button.style.display = 'none';
        }
    }
    
    /**
     * Gets selected items from the dashboard
     * @returns {Array} An array of selected items
     */
    function getSelectedItems() {
        // Try various selectors for checkboxes
        const checkboxSelectors = [
            '.stickara-item-checkbox:checked',
            '.stickara-dashboard-item input[type="checkbox"]:checked',
            '.dashboard-item input[type="checkbox"]:checked',
            '.note-item input[type="checkbox"]:checked',
            '.highlight-item input[type="checkbox"]:checked'
        ];
        
        // Combine all selectors
        const selector = checkboxSelectors.join(', ');
        const checkedCheckboxes = document.querySelectorAll(selector);
        
        if (checkedCheckboxes.length === 0) {
            return [];
        }
        
        // Map checkboxes to items
        return Array.from(checkedCheckboxes).map(checkbox => {
            const itemElement = checkbox.closest('.stickara-dashboard-item, .dashboard-item, .note-item, .highlight-item');
            if (!itemElement) return null;
            
            // Get item data
            const itemId = itemElement.dataset.itemId || itemElement.id || '';
            const itemType = itemElement.dataset.itemType || 
                             (itemElement.classList.contains('note-item') ? 'note' : 
                              itemElement.classList.contains('highlight-item') ? 'highlight' : 'unknown');
            const itemUrl = itemElement.dataset.itemUrl || '';
            const itemText = itemElement.querySelector('.stickara-item-text, .item-content, .note-content, .highlight-content')?.textContent || '';
            
            return {
                id: itemId,
                type: itemType,
                url: itemUrl,
                text: itemText
            };
        }).filter(Boolean); // Remove null items
    }
    
    /**
     * Handles button click
     */
    function handleButtonClick() {
        console.log("Stickara Direct GDocs: Button clicked");
        
        // First try to use the export modal from the main integration
        if (window.StickaraGoogleDocsExport && typeof window.StickaraGoogleDocsExport.showExportModal === 'function') {
            window.StickaraGoogleDocsExport.showExportModal();
            return;
        }
        
        // Fallback to a simple confirmation
        const selectedItems = getSelectedItems();
        const confirmMessage = `Export ${selectedItems.length} item${selectedItems.length !== 1 ? 's' : ''} to Google Docs?`;
        
        if (confirm(confirmMessage)) {
            alert('This feature is coming soon! The Google Docs export functionality is currently being implemented.');
        }
    }
    
    // Initialize immediately
    init();
    
    // Also initialize when the document is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    }
    
    // Also initialize when the window loads
    window.addEventListener('load', init);
})();

console.log("Stickara: Direct Google Docs Button Injector Loaded");
