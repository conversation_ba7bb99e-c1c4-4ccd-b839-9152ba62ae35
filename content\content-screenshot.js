// --- START OF FILE content-screenshot.js ---

// Assume state variables (NOTE_ID), utils (insertNodeAtCursor, scheduleSave, isCanvasEmpty)
// and toolbar functions (createToolButton) are available globally or imported/defined elsewhere.

const SCREENSHOT_MODAL_ID = 'Stickara-screenshot-modal';
const SCREENSHOT_CANVAS_ID = 'Stickara-screenshot-canvas';
const SCREENSHOT_TOOLBAR_ID = 'Stickara-screenshot-toolbar';

// --- Modal and Canvas Element References ---
let screenshotAnnotationModal = null;
let screenshotCanvas = null;
let screenshotCtx = null;
let screenshotImg = new Image(); // Holds the original screenshot

/**
 * Creates a screenshot canvas with enhanced error handling for YouTube compatibility
 * @returns {HTMLCanvasElement|null} The created canvas element or null if creation failed
 */
function createScreenshotCanvas() {
    try {
        // Check if canvas already exists
        let existingCanvas = document.getElementById(SCREENSHOT_CANVAS_ID);
        if (existingCanvas) {
            console.log("Stickara: Reusing existing screenshot canvas");
            return existingCanvas;
        }

        // Create new canvas element
        const canvas = document.createElement('canvas');
        canvas.id = SCREENSHOT_CANVAS_ID;

        // Apply styling with YouTube compatibility
        canvas.style.display = 'block';
        canvas.style.cursor = 'crosshair';
        canvas.style.backgroundColor = '#ffffff';
        canvas.style.border = '1px solid #ccc';
        canvas.style.maxWidth = '100%';
        canvas.style.maxHeight = '100%';
        canvas.style.objectFit = 'contain';

        // Enhanced styling for better rendering
        canvas.style.imageRendering = 'pixelated';
        canvas.style.imageRendering = '-moz-crisp-edges';
        canvas.style.imageRendering = 'crisp-edges';

        // Ensure canvas is properly initialized
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error("Stickara: Failed to get 2D context for screenshot canvas");
            return null;
        }

        // Store context reference
        screenshotCtx = ctx;

        console.log("Stickara: Screenshot canvas created successfully");
        return canvas;

    } catch (error) {
        console.error("Stickara: Error creating screenshot canvas:", error);
        return null;
    }
}

// --- Annotation State ---
let currentAnnotationTool = 'arrow'; // 'arrow', 'text', 'rect', 'circle', 'line', 'pen', 'highlight', 'callout', 'blur'
let annotationColor = '#FF0000'; // Default Red
let annotationLineWidth = 2; // Default Line Width
let isAnnotating = false; // Flag if mouse button is currently down for drawing
let annotationStartX, annotationStartY; // Start coordinates for shapes/lines
let annotations = []; // Array to store annotation objects using PHYSICAL pixel coordinates
                      // { type, color, lineWidth, x1, y1, x2?, y2?, text?, points?[], radius?, width?, height? }
let tempCanvasData = null; // Stores canvas state (ImageData) for shape previews
let blurRadius = 10; // Default blur radius for blur tool

// --- Drag and Drop State ---
let selectedAnnotation = null; // Currently selected annotation for dragging
let isDraggingAnnotation = false; // Flag if currently dragging an annotation
let annotationDragStartX, annotationDragStartY; // Start coordinates for annotation dragging
let annotationDragOffsetX, annotationDragOffsetY; // Offset from annotation center to mouse position
let canvasContainer = null; // Reference to canvas container for scroll operations

// --- Auto-scroll State ---
let autoScrollInterval = null; // Interval for auto-scrolling
let autoScrollSpeed = 2; // Pixels per auto-scroll step
let highlightOpacity = 0.5; // Default opacity for highlight tool

// --- Modal Drag State ---
let isModalDragging = false; // Flag if modal is being dragged
let modalDragStartX = 0; // Modal drag start X coordinate
let modalDragStartY = 0; // Modal drag start Y coordinate
let modalInitialLeft = 0; // Modal initial left position
let modalInitialTop = 0; // Modal initial top position

// --- Enhanced Annotation State ---
let annotationHistory = []; // Undo/redo history
let historyIndex = -1; // Current position in history
let maxHistorySize = 50; // Maximum undo steps
let annotationLayers = [{ name: 'Layer 1', visible: true, annotations: [] }]; // Layer management
let currentLayer = 0; // Active layer index
let isEraserMode = false; // Eraser tool state
let brushOpacity = 1.0; // Brush opacity (0.0 - 1.0)
let shadowEnabled = false; // Drop shadow toggle
let shadowColor = '#000000'; // Shadow color
let shadowBlur = 5; // Shadow blur radius
let shadowOffsetX = 2; // Shadow X offset
let shadowOffsetY = 2; // Shadow Y offset
let gridEnabled = false; // Snap-to-grid toggle
let gridSize = 10; // Grid size in pixels
let zoomLevel = 1.0; // Current zoom level
let panX = 0; // Pan offset X
let panY = 0; // Pan offset Y
let selectedAnnotations = []; // Multiple selection support
let groupedAnnotations = []; // Grouped annotations
let toolPresets = {}; // Saved tool configurations
let borderStyle = 'solid'; // Border style: solid, dashed, dotted
let fillPattern = 'solid'; // Fill pattern: solid, gradient, pattern
let gradientColors = ['#FF0000', '#0000FF']; // Gradient color stops
let measurementUnit = 'px'; // Measurement unit: px, mm, in
let connectorSnapDistance = 20; // Snap distance for connectors

// --- Advanced Tool State ---
let brushSize = 5; // Variable brush size
let pressureSensitivity = true; // Pressure sensitivity simulation
let eraserSize = 10; // Eraser size
let shapeVertices = 3; // For polygon tool
let measurementMode = false; // Measurement tool active
let alignmentGuides = []; // Alignment guide lines
let snapToObjects = true; // Snap to other annotations

// --- Annotation Color Palette ---
const ANNOTATION_COLORS = ['#FF0000', '#0000FF', '#00FF00', '#000000', '#FFFF00', '#FFA500', '#FFFFFF', '#FF00FF', '#00FFFF', '#808080']; // Extended color palette

// --- Advanced Annotation Functions ---

/**
 * Saves current annotation state to history for undo/redo
 */
function saveToHistory() {
    // Remove any history after current index (when user made changes after undo)
    annotationHistory = annotationHistory.slice(0, historyIndex + 1);

    // Add current state to history
    const currentState = {
        annotations: JSON.parse(JSON.stringify(annotations)),
        layers: JSON.parse(JSON.stringify(annotationLayers)),
        timestamp: Date.now()
    };

    annotationHistory.push(currentState);
    historyIndex++;

    // Limit history size
    if (annotationHistory.length > maxHistorySize) {
        annotationHistory.shift();
        historyIndex--;
    }

    updateUndoRedoButtons();
}

/**
 * Performs undo operation
 */
function performUndo() {
    if (historyIndex > 0) {
        historyIndex--;
        const state = annotationHistory[historyIndex];
        annotations = JSON.parse(JSON.stringify(state.annotations));
        annotationLayers = JSON.parse(JSON.stringify(state.layers));
        redrawScreenshotCanvas();
        updateUndoRedoButtons();
        updateLayerPanel();
    }
}

/**
 * Performs redo operation
 */
function performRedo() {
    if (historyIndex < annotationHistory.length - 1) {
        historyIndex++;
        const state = annotationHistory[historyIndex];
        annotations = JSON.parse(JSON.stringify(state.annotations));
        annotationLayers = JSON.parse(JSON.stringify(state.layers));
        redrawScreenshotCanvas();
        updateUndoRedoButtons();
        updateLayerPanel();
    }
}

/**
 * Updates undo/redo button states
 */
function updateUndoRedoButtons() {
    const undoBtn = document.querySelector('[data-tool="undo"]');
    const redoBtn = document.querySelector('[data-tool="redo"]');

    if (undoBtn) {
        undoBtn.disabled = historyIndex <= 0;
        undoBtn.style.opacity = undoBtn.disabled ? '0.5' : '1';
    }

    if (redoBtn) {
        redoBtn.disabled = historyIndex >= annotationHistory.length - 1;
        redoBtn.style.opacity = redoBtn.disabled ? '0.5' : '1';
    }
}

/**
 * Snaps coordinates to grid if enabled
 */
function snapToGrid(x, y) {
    if (!gridEnabled) return { x, y };

    return {
        x: Math.round(x / gridSize) * gridSize,
        y: Math.round(y / gridSize) * gridSize
    };
}

/**
 * Applies drop shadow to context if enabled
 */
function applyDropShadow(ctx) {
    if (shadowEnabled) {
        ctx.shadowColor = shadowColor;
        ctx.shadowBlur = shadowBlur;
        ctx.shadowOffsetX = shadowOffsetX;
        ctx.shadowOffsetY = shadowOffsetY;
    } else {
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
    }
}

/**
 * Sets border style for context
 */
function setBorderStyle(ctx) {
    switch (borderStyle) {
        case 'dashed':
            ctx.setLineDash([10, 5]);
            break;
        case 'dotted':
            ctx.setLineDash([2, 3]);
            break;
        case 'solid':
        default:
            ctx.setLineDash([]);
            break;
    }
}

/**
 * Creates gradient fill if pattern is gradient
 */
function createGradientFill(ctx, x1, y1, x2, y2) {
    if (fillPattern !== 'gradient') return annotationColor;

    const gradient = ctx.createLinearGradient(x1, y1, x2, y2);
    gradient.addColorStop(0, gradientColors[0]);
    gradient.addColorStop(1, gradientColors[1]);
    return gradient;
}

/**
 * Updates eraser mode visual state
 */
function updateEraserMode(eraserBtn) {
    if (isEraserMode) {
        eraserBtn.classList.add('active');
        eraserBtn.style.backgroundColor = '#ff6b6b';
        eraserBtn.style.color = 'white';
        eraserBtn.title = 'Exit Eraser Mode - Click annotations to erase them';
        screenshotCanvas.style.cursor = 'crosshair';
    } else {
        eraserBtn.classList.remove('active');
        eraserBtn.style.backgroundColor = '';
        eraserBtn.style.color = '';
        eraserBtn.title = 'Eraser Tool';
        screenshotCanvas.style.cursor = 'crosshair';
    }
}

/**
 * Updates layer panel (placeholder for future layer management)
 */
function updateLayerPanel() {
    // Placeholder for layer management UI updates
    // This would update layer visibility, names, etc.
}

/**
 * Calculates distance from point to line segment
 */
function distanceFromPointToLine(px, py, x1, y1, x2, y2) {
    const A = px - x1;
    const B = py - y1;
    const C = x2 - x1;
    const D = y2 - y1;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;

    if (lenSq === 0) return Math.sqrt(A * A + B * B);

    let param = dot / lenSq;

    let xx, yy;

    if (param < 0) {
        xx = x1;
        yy = y1;
    } else if (param > 1) {
        xx = x2;
        yy = y2;
    } else {
        xx = x1 + param * C;
        yy = y1 + param * D;
    }

    const dx = px - xx;
    const dy = py - yy;
    return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Handles eraser functionality
 */
function handleEraserClick(x, y) {
    if (!isEraserMode) return false;

    // Find annotations to erase
    const toErase = [];
    for (let i = annotations.length - 1; i >= 0; i--) {
        if (hitTestAnnotation(x, y, annotations[i])) {
            toErase.push(i);
        }
    }

    // Remove erased annotations
    if (toErase.length > 0) {
        saveToHistory(); // Save state before erasing
        toErase.forEach(index => annotations.splice(index, 1));
        redrawScreenshotCanvas();
        return true;
    }

    return false;
}

// --- Helper Functions for Scroll-Aware Coordinates ---

/**
 * Converts mouse event coordinates to canvas physical coordinates accounting for scroll
 * @param {MouseEvent} e - Mouse event
 * @returns {Object} - {x, y} in physical canvas coordinates
 */
function getScrollAwareCanvasCoordinates(e) {
    if (!screenshotCanvas || !canvasContainer) return { x: 0, y: 0 };

    const dpr = window.devicePixelRatio || 1;

    // Use offsetX and offsetY which automatically account for element positioning
    // These give the position relative to the canvas element itself
    const logicalX = e.offsetX;
    const logicalY = e.offsetY;

    // Convert to physical coordinates
    const physicalX = logicalX * dpr;
    const physicalY = logicalY * dpr;



    return { x: physicalX, y: physicalY };
}

/**
 * Converts physical canvas coordinates to logical coordinates for display
 * @param {number} physicalX - Physical X coordinate
 * @param {number} physicalY - Physical Y coordinate
 * @returns {Object} - {x, y} in logical coordinates
 */
function physicalToLogical(physicalX, physicalY) {
    const dpr = window.devicePixelRatio || 1;
    return {
        x: physicalX / dpr,
        y: physicalY / dpr
    };
}

/**
 * Tests if a point hits an annotation
 * @param {number} x - Physical X coordinate
 * @param {number} y - Physical Y coordinate
 * @param {Object} annotation - Annotation object
 * @returns {boolean} - True if hit
 */
function hitTestAnnotation(x, y, annotation) {
    const hitThreshold = 10; // Pixels for hit testing

    switch (annotation.type) {
        case 'rect':
        case 'highlight':
        case 'blur':
            const left = Math.min(annotation.x1, annotation.x2);
            const right = Math.max(annotation.x1, annotation.x2);
            const top = Math.min(annotation.y1, annotation.y2);
            const bottom = Math.max(annotation.y1, annotation.y2);
            return x >= left - hitThreshold && x <= right + hitThreshold &&
                   y >= top - hitThreshold && y <= bottom + hitThreshold;

        case 'circle':
            const centerX = (annotation.x1 + annotation.x2) / 2;
            const centerY = (annotation.y1 + annotation.y2) / 2;
            const radius = Math.sqrt(Math.pow(annotation.x2 - annotation.x1, 2) + Math.pow(annotation.y2 - annotation.y1, 2)) / 2;
            const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));
            return distance <= radius + hitThreshold;

        case 'line':
        case 'arrow':
            return distanceFromPointToLine(x, y, annotation.x1, annotation.y1, annotation.x2, annotation.y2) <= hitThreshold;

        case 'text':
        case 'callout':
            // Simple bounding box for text (could be improved with actual text metrics)
            const textWidth = annotation.text ? annotation.text.length * 8 : 50; // Rough estimate
            const textHeight = 20;
            return x >= annotation.x1 - hitThreshold && x <= annotation.x1 + textWidth + hitThreshold &&
                   y >= annotation.y1 - textHeight - hitThreshold && y <= annotation.y1 + hitThreshold;

        case 'pen':
        case 'brush':
            if (!annotation.points || annotation.points.length === 0) return false;
            return annotation.points.some(point =>
                Math.sqrt(Math.pow(x - point.x, 2) + Math.pow(y - point.y, 2)) <= hitThreshold
            );

        case 'triangle':
        case 'diamond':
        case 'star':
        case 'polygon':
            // For complex shapes, use bounding box for now (could be improved with point-in-polygon)
            const shapeBounds = getAnnotationBounds(annotation);
            if (!shapeBounds) return false;
            return x >= shapeBounds.left - hitThreshold && x <= shapeBounds.left + shapeBounds.width + hitThreshold &&
                   y >= shapeBounds.top - hitThreshold && y <= shapeBounds.top + shapeBounds.height + hitThreshold;

        case 'connector':
            return distanceFromPointToLine(x, y, annotation.x1, annotation.y1, annotation.x2, annotation.y2) <= hitThreshold;

        case 'measurement':
            return distanceFromPointToLine(x, y, annotation.x1, annotation.y1, annotation.x2, annotation.y2) <= hitThreshold;

        default:
            return false;
    }
}

/**
 * Calculates distance from a point to a line segment
 * @param {number} px - Point X
 * @param {number} py - Point Y
 * @param {number} x1 - Line start X
 * @param {number} y1 - Line start Y
 * @param {number} x2 - Line end X
 * @param {number} y2 - Line end Y
 * @returns {number} - Distance from point to line
 */
function distanceFromPointToLine(px, py, x1, y1, x2, y2) {
    const A = px - x1;
    const B = py - y1;
    const C = x2 - x1;
    const D = y2 - y1;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;

    if (lenSq === 0) {
        return Math.sqrt(A * A + B * B);
    }

    let param = dot / lenSq;

    let xx, yy;

    if (param < 0) {
        xx = x1;
        yy = y1;
    } else if (param > 1) {
        xx = x2;
        yy = y2;
    } else {
        xx = x1 + param * C;
        yy = y1 + param * D;
    }

    const dx = px - xx;
    const dy = py - yy;
    return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Handles auto-scrolling when dragging near viewport edges
 * @param {MouseEvent} e - Mouse event
 */
function handleAutoScroll(e) {
    if (!canvasContainer || !isDraggingAnnotation) return;

    const containerRect = canvasContainer.getBoundingClientRect();
    const scrollThreshold = 50; // Distance from edge to trigger auto-scroll

    // Calculate mouse position relative to container
    const mouseX = e.clientX - containerRect.left;
    const mouseY = e.clientY - containerRect.top;

    let scrollX = 0;
    let scrollY = 0;

    // Check horizontal scrolling
    if (mouseX < scrollThreshold) {
        scrollX = -autoScrollSpeed;
    } else if (mouseX > containerRect.width - scrollThreshold) {
        scrollX = autoScrollSpeed;
    }

    // Check vertical scrolling
    if (mouseY < scrollThreshold) {
        scrollY = -autoScrollSpeed;
    } else if (mouseY > containerRect.height - scrollThreshold) {
        scrollY = autoScrollSpeed;
    }

    // Apply scrolling
    if (scrollX !== 0 || scrollY !== 0) {
        if (!autoScrollInterval) {
            autoScrollInterval = setInterval(() => {
                canvasContainer.scrollLeft += scrollX;
                canvasContainer.scrollTop += scrollY;
            }, 16); // ~60fps
        }
    } else {
        stopAutoScroll();
    }
}

/**
 * Stops auto-scrolling
 */
function stopAutoScroll() {
    if (autoScrollInterval) {
        clearInterval(autoScrollInterval);
        autoScrollInterval = null;
    }
}

/**
 * Moves an annotation by the specified offset
 * @param {Object} annotation - Annotation to move
 * @param {number} deltaX - X offset in physical coordinates
 * @param {number} deltaY - Y offset in physical coordinates
 */
function moveAnnotation(annotation, deltaX, deltaY) {
    switch (annotation.type) {
        case 'rect':
        case 'circle':
        case 'line':
        case 'arrow':
        case 'highlight':
        case 'blur':
            annotation.x1 += deltaX;
            annotation.y1 += deltaY;
            annotation.x2 += deltaX;
            annotation.y2 += deltaY;
            break;

        case 'text':
        case 'callout':
            annotation.x1 += deltaX;
            annotation.y1 += deltaY;
            if (annotation.x2 !== undefined) annotation.x2 += deltaX;
            if (annotation.y2 !== undefined) annotation.y2 += deltaY;
            break;

        case 'pen':
        case 'brush':
            if (annotation.points) {
                annotation.points.forEach(point => {
                    point.x += deltaX;
                    point.y += deltaY;
                });
            }
            break;

        case 'triangle':
        case 'diamond':
        case 'star':
        case 'polygon':
        case 'connector':
        case 'measurement':
            annotation.x1 += deltaX;
            annotation.y1 += deltaY;
            annotation.x2 += deltaX;
            annotation.y2 += deltaY;
            if (annotation.vertices) {
                annotation.vertices.forEach(vertex => {
                    vertex.x += deltaX;
                    vertex.y += deltaY;
                });
            }
            break;
    }
}

/**
 * Gets the bounding box of an annotation
 * @param {Object} annotation - Annotation object
 * @returns {Object} - {left, top, width, height} in physical coordinates
 */
function getAnnotationBounds(annotation) {
    switch (annotation.type) {
        case 'rect':
        case 'circle':
        case 'line':
        case 'arrow':
        case 'highlight':
        case 'blur':
            const left = Math.min(annotation.x1, annotation.x2);
            const right = Math.max(annotation.x1, annotation.x2);
            const top = Math.min(annotation.y1, annotation.y2);
            const bottom = Math.max(annotation.y1, annotation.y2);
            return {
                left: left,
                top: top,
                width: right - left,
                height: bottom - top
            };

        case 'text':
        case 'callout':
            // Estimate text bounds (could be improved with actual text metrics)
            const textWidth = annotation.text ? annotation.text.length * 8 : 50;
            const textHeight = 20;
            return {
                left: annotation.x1,
                top: annotation.y1 - textHeight,
                width: textWidth,
                height: textHeight
            };

        case 'pen':
        case 'brush':
            if (!annotation.points || annotation.points.length === 0) return null;
            let minX = annotation.points[0].x;
            let maxX = annotation.points[0].x;
            let minY = annotation.points[0].y;
            let maxY = annotation.points[0].y;

            annotation.points.forEach(point => {
                minX = Math.min(minX, point.x);
                maxX = Math.max(maxX, point.x);
                minY = Math.min(minY, point.y);
                maxY = Math.max(maxY, point.y);
            });

            return {
                left: minX,
                top: minY,
                width: maxX - minX,
                height: maxY - minY
            };

        case 'triangle':
        case 'diamond':
        case 'star':
        case 'polygon':
        case 'connector':
        case 'measurement':
            const shapeLeft = Math.min(annotation.x1, annotation.x2);
            const shapeRight = Math.max(annotation.x1, annotation.x2);
            const shapeTop = Math.min(annotation.y1, annotation.y2);
            const shapeBottom = Math.max(annotation.y1, annotation.y2);
            return {
                left: shapeLeft,
                top: shapeTop,
                width: shapeRight - shapeLeft,
                height: shapeBottom - shapeTop
            };

        default:
            return null;
    }
}

/** Helper: Generates a safe filename prefix from the current URL */
function generateScreenshotFilenamePrefix() {
    try {
        const urlObj = new URL(window.location.href);
        const hostname = urlObj.hostname.replace(/^www\./, '');
        const safeHostname = hostname.replace(/[^a-z0-9\.\-]/gi, '_').substring(0, 50);
        const timestamp = new Date().toISOString().replace(/[:\-]/g, '').replace('T', '_').split('.')[0];
        return `SB_${safeHostname}_${timestamp}`;
    } catch (e) {
        console.warn("SB Screenshot: Error generating filename from URL", e);
        return `SB_screenshot_${Date.now()}`;
    }
}

/** Helper: Redraws the canvas with the base image and all stored annotations (using physical pixels) */
function redrawScreenshotCanvas(drawingPreview = false) {
    if (!screenshotCanvas || !screenshotCtx || !screenshotImg.src) {
        console.warn("redrawScreenshotCanvas: Canvas, context, or image source missing.");
        return;
    }

    const ctx = screenshotCtx; // Alias for brevity
    const canvasWidth = screenshotCanvas.width; // Physical width
    const canvasHeight = screenshotCanvas.height; // Physical height

    // Optimization: Restore from snapshot if only drawing a shape preview
    if (drawingPreview && tempCanvasData) {
        try {
            ctx.putImageData(tempCanvasData, 0, 0); // putImageData uses physical pixels
            return; // Preview drawing is handled by the mousemove listener
        } catch (e) {
            console.error("Error putting temp canvas data:", e);
            tempCanvasData = null; // Invalidate temp data if restore fails
        }
    }

    // Full redraw: Clear canvas (physical dimensions)
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // --- Enhanced Image Quality Settings for Maximum Clarity ---
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = "high";

    // Optimize rendering for crisp, clear images
    ctx.globalCompositeOperation = 'source-over';
    ctx.filter = 'none'; // Ensure no unwanted filters are applied
    ctx.textRenderingOptimization = 'optimizeQuality';

    // Save context state before drawing
    ctx.save();

    // Apply pixel-perfect rendering settings
    ctx.setTransform(1, 0, 0, 1, 0, 0); // Reset any transforms

    // Error handling for drawImage with enhanced quality
    try {
        // For maximum quality, use nearest-neighbor for pixel-perfect images
        // or high-quality smoothing for photographs
        const isPixelArt = canvasWidth < screenshotImg.naturalWidth * 0.5;

        if (isPixelArt) {
            // For pixel art or heavily downscaled images, disable smoothing
            ctx.imageSmoothingEnabled = false;
        } else {
            // For photographs and normal scaling, use high-quality smoothing
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = "high";
        }

        // Draw the base image using physical dimensions with optimal quality
        ctx.drawImage(screenshotImg, 0, 0, canvasWidth, canvasHeight);

        // Restore smoothing for annotations
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = "high";

    } catch (e) {
        console.error("Error drawing base screenshot image:", e);
        ctx.fillStyle = 'red';
        ctx.font = '16px Arial';
        // Draw error message using physical coordinates
        ctx.fillText("Error loading base image", 10, 30);
        ctx.restore();
        return; // Stop rendering if base image fails
    }

    // Restore context state
    ctx.restore();

    // Draw all saved annotations (using PHYSICAL pixel coordinates)
    annotations.forEach((anno, index) => {
        ctx.strokeStyle = anno.color;
        ctx.fillStyle = anno.color;
        ctx.lineWidth = anno.lineWidth; // Line width in physical pixels
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        ctx.font = `${14}px sans-serif`; // Font size in physical pixels (might need adjustment if too small/large)
        ctx.beginPath();

        // Draw selection indicator if this annotation is selected
        const isSelected = selectedAnnotation === anno;
        if (isSelected) {
            ctx.save();
            ctx.strokeStyle = '#0066ff';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);

            // Draw selection box around annotation
            const bounds = getAnnotationBounds(anno);
            if (bounds) {
                const padding = 8;
                ctx.strokeRect(
                    bounds.left - padding,
                    bounds.top - padding,
                    bounds.width + padding * 2,
                    bounds.height + padding * 2
                );
            }
            ctx.restore();

            // Reset styles for drawing the annotation
            ctx.strokeStyle = anno.color;
            ctx.fillStyle = anno.color;
            ctx.lineWidth = anno.lineWidth;
            ctx.setLineDash([]);
        }

        try {
            switch (anno.type) {
                case 'arrow':
                    const headlen = 10 * (anno.lineWidth / 2); // Arrowhead size based on physical line width
                    const dxA = anno.x2 - anno.x1;
                    const dyA = anno.y2 - anno.y1;
                    if (Math.abs(dxA) < 0.1 && Math.abs(dyA) < 0.1) break;
                    const angleA = Math.atan2(dyA, dxA);
                    ctx.moveTo(anno.x1, anno.y1); // Use physical coords
                    ctx.lineTo(anno.x2, anno.y2); // Use physical coords
                    ctx.moveTo(anno.x2, anno.y2);
                    ctx.lineTo(anno.x2 - headlen * Math.cos(angleA - Math.PI / 6), anno.y2 - headlen * Math.sin(angleA - Math.PI / 6));
                    ctx.moveTo(anno.x2, anno.y2);
                    ctx.lineTo(anno.x2 - headlen * Math.cos(angleA + Math.PI / 6), anno.y2 - headlen * Math.sin(angleA + Math.PI / 6));
                    ctx.stroke();
                    break;
                case 'text':
                    if (anno.text) {
                        ctx.textAlign = 'left';
                        ctx.textBaseline = 'top';
                        // Set font size based on line width for better visibility
                        const fontSize = Math.max(14, anno.lineWidth * 7); // Minimum 14px, scales with line width
                        ctx.font = `bold ${fontSize}px sans-serif`;

                        // Add a subtle background for better text visibility on any background
                        const textMetrics = ctx.measureText(anno.text);
                        const textHeight = fontSize * 1.2; // Approximate text height

                        // Draw text background
                        ctx.save();
                        ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
                        ctx.fillRect(
                            anno.x1 - 2,
                            anno.y1 - 2,
                            textMetrics.width + 4,
                            textHeight + 4
                        );
                        ctx.restore();

                        // Draw the text
                        ctx.fillStyle = anno.color;
                        ctx.fillText(anno.text, anno.x1, anno.y1); // Use physical coords
                    }
                    break;
                case 'rect':
                    ctx.strokeRect(anno.x1, anno.y1, anno.x2 - anno.x1, anno.y2 - anno.y1); // Use physical coords
                    break;
                case 'circle':
                    // Calculations use physical coords
                    const radiusC = Math.sqrt(Math.pow(anno.x2 - anno.x1, 2) + Math.pow(anno.y2 - anno.y1, 2)) / 2;
                    if (radiusC > 0.5) {
                        const centerXC = anno.x1 + (anno.x2 - anno.x1) / 2;
                        const centerYC = anno.y1 + (anno.y2 - anno.y1) / 2;
                        ctx.arc(centerXC, centerYC, Math.abs(radiusC), 0, 2 * Math.PI);
                        ctx.stroke();
                    }
                    break;
                case 'line':
                    ctx.moveTo(anno.x1, anno.y1); // Use physical coords
                    ctx.lineTo(anno.x2, anno.y2); // Use physical coords
                    ctx.stroke();
                    break;
                case 'pen':
                    if (anno.points && anno.points.length > 1) {
                        ctx.moveTo(anno.points[0].x, anno.points[0].y); // Use physical coords
                        for (let i = 1; i < anno.points.length; i++) {
                            ctx.lineTo(anno.points[i].x, anno.points[i].y); // Use physical coords
                        }
                        ctx.stroke();
                    } else if (anno.points && anno.points.length === 1) {
                        // Dot size based on physical lineWidth
                        ctx.arc(anno.points[0].x, anno.points[0].y, anno.lineWidth / 2, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                    break;

                case 'brush':
                    // Enhanced brush with variable size and opacity
                    if (anno.points && anno.points.length > 0) {
                        ctx.save();
                        ctx.globalAlpha = anno.opacity || brushOpacity;
                        ctx.lineCap = 'round';
                        ctx.lineJoin = 'round';

                        if (anno.points.length === 1) {
                            // Single point - draw a circle
                            const size = anno.brushSize || anno.lineWidth;
                            ctx.beginPath();
                            ctx.arc(anno.points[0].x, anno.points[0].y, size / 2, 0, 2 * Math.PI);
                            ctx.fill();
                        } else {
                            // Multiple points - draw smooth curve
                            ctx.beginPath();
                            ctx.moveTo(anno.points[0].x, anno.points[0].y);

                            for (let i = 1; i < anno.points.length - 1; i++) {
                                const currentPoint = anno.points[i];
                                const nextPoint = anno.points[i + 1];
                                const controlX = (currentPoint.x + nextPoint.x) / 2;
                                const controlY = (currentPoint.y + nextPoint.y) / 2;
                                ctx.quadraticCurveTo(currentPoint.x, currentPoint.y, controlX, controlY);
                            }

                            // Draw to the last point
                            if (anno.points.length > 1) {
                                const lastPoint = anno.points[anno.points.length - 1];
                                ctx.lineTo(lastPoint.x, lastPoint.y);
                            }

                            ctx.stroke();
                        }
                        ctx.restore();
                    }
                    break;

                case 'triangle':
                    // Draw triangle
                    const triWidth = anno.x2 - anno.x1;
                    const triCenterX = anno.x1 + triWidth / 2;

                    ctx.beginPath();
                    ctx.moveTo(triCenterX, anno.y1); // Top point
                    ctx.lineTo(anno.x1, anno.y2); // Bottom left
                    ctx.lineTo(anno.x2, anno.y2); // Bottom right
                    ctx.closePath();

                    if (anno.filled) {
                        const fillStyle = createGradientFill(ctx, anno.x1, anno.y1, anno.x2, anno.y2);
                        ctx.fillStyle = fillStyle;
                        ctx.fill();
                    }
                    ctx.stroke();
                    break;

                case 'diamond':
                    // Draw diamond (rhombus)
                    const diamondCenterX = anno.x1 + (anno.x2 - anno.x1) / 2;
                    const diamondCenterY = anno.y1 + (anno.y2 - anno.y1) / 2;

                    ctx.beginPath();
                    ctx.moveTo(diamondCenterX, anno.y1); // Top
                    ctx.lineTo(anno.x2, diamondCenterY); // Right
                    ctx.lineTo(diamondCenterX, anno.y2); // Bottom
                    ctx.lineTo(anno.x1, diamondCenterY); // Left
                    ctx.closePath();

                    if (anno.filled) {
                        const fillStyle = createGradientFill(ctx, anno.x1, anno.y1, anno.x2, anno.y2);
                        ctx.fillStyle = fillStyle;
                        ctx.fill();
                    }
                    ctx.stroke();
                    break;

                case 'star':
                    // Draw star
                    const starCenterX = anno.x1 + (anno.x2 - anno.x1) / 2;
                    const starCenterY = anno.y1 + (anno.y2 - anno.y1) / 2;
                    const starRadius = Math.min(Math.abs(anno.x2 - anno.x1), Math.abs(anno.y2 - anno.y1)) / 2;
                    const starPoints = anno.starPoints || 5;

                    ctx.beginPath();
                    for (let i = 0; i < starPoints * 2; i++) {
                        const angle = (i * Math.PI) / starPoints;
                        const radius = i % 2 === 0 ? starRadius : starRadius * 0.5;
                        const x = starCenterX + Math.cos(angle - Math.PI / 2) * radius;
                        const y = starCenterY + Math.sin(angle - Math.PI / 2) * radius;

                        if (i === 0) {
                            ctx.moveTo(x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    }
                    ctx.closePath();

                    if (anno.filled) {
                        const fillStyle = createGradientFill(ctx, anno.x1, anno.y1, anno.x2, anno.y2);
                        ctx.fillStyle = fillStyle;
                        ctx.fill();
                    }
                    ctx.stroke();
                    break;

                case 'polygon':
                    // Draw regular polygon
                    const polyCenterX = anno.x1 + (anno.x2 - anno.x1) / 2;
                    const polyCenterY = anno.y1 + (anno.y2 - anno.y1) / 2;
                    const polyRadius = Math.min(Math.abs(anno.x2 - anno.x1), Math.abs(anno.y2 - anno.y1)) / 2;
                    const polyVertices = anno.vertices || shapeVertices;

                    ctx.beginPath();
                    for (let i = 0; i < polyVertices; i++) {
                        const angle = (i * 2 * Math.PI) / polyVertices;
                        const x = polyCenterX + Math.cos(angle - Math.PI / 2) * polyRadius;
                        const y = polyCenterY + Math.sin(angle - Math.PI / 2) * polyRadius;

                        if (i === 0) {
                            ctx.moveTo(x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    }
                    ctx.closePath();

                    if (anno.filled) {
                        const fillStyle = createGradientFill(ctx, anno.x1, anno.y1, anno.x2, anno.y2);
                        ctx.fillStyle = fillStyle;
                        ctx.fill();
                    }
                    ctx.stroke();
                    break;

                case 'connector':
                    // Smart connector line with endpoints
                    ctx.beginPath();
                    ctx.moveTo(anno.x1, anno.y1);
                    ctx.lineTo(anno.x2, anno.y2);
                    ctx.stroke();

                    // Draw connection points
                    const connectorRadius = 4;
                    ctx.beginPath();
                    ctx.arc(anno.x1, anno.y1, connectorRadius, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.beginPath();
                    ctx.arc(anno.x2, anno.y2, connectorRadius, 0, 2 * Math.PI);
                    ctx.fill();
                    break;

                case 'measurement':
                    // Measurement tool with dimension display
                    ctx.beginPath();
                    ctx.moveTo(anno.x1, anno.y1);
                    ctx.lineTo(anno.x2, anno.y2);
                    ctx.stroke();

                    // Calculate distance
                    const distance = Math.sqrt(Math.pow(anno.x2 - anno.x1, 2) + Math.pow(anno.y2 - anno.y1, 2));
                    let displayDistance;

                    switch (measurementUnit) {
                        case 'mm':
                            displayDistance = (distance * 0.264583).toFixed(1) + ' mm';
                            break;
                        case 'in':
                            displayDistance = (distance * 0.0104167).toFixed(2) + ' in';
                            break;
                        default:
                            displayDistance = Math.round(distance) + ' px';
                    }

                    // Draw measurement text
                    const midX = (anno.x1 + anno.x2) / 2;
                    const midY = (anno.y1 + anno.y2) / 2;

                    ctx.save();
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                    ctx.strokeStyle = anno.color;
                    ctx.lineWidth = 1;

                    const textMetrics = ctx.measureText(displayDistance);
                    const textPadding = 4;
                    ctx.fillRect(
                        midX - textMetrics.width / 2 - textPadding,
                        midY - 10 - textPadding,
                        textMetrics.width + textPadding * 2,
                        20 + textPadding * 2
                    );
                    ctx.strokeRect(
                        midX - textMetrics.width / 2 - textPadding,
                        midY - 10 - textPadding,
                        textMetrics.width + textPadding * 2,
                        20 + textPadding * 2
                    );

                    ctx.fillStyle = anno.color;
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText(displayDistance, midX, midY);
                    ctx.restore();
                    break;

                case 'highlight':
                    // Draw a semi-transparent highlight rectangle
                    ctx.save();
                    ctx.globalAlpha = highlightOpacity;
                    ctx.fillStyle = anno.color;
                    ctx.fillRect(anno.x1, anno.y1, anno.x2 - anno.x1, anno.y2 - anno.y1);
                    ctx.restore();
                    break;

                case 'callout':
                    // Draw a callout bubble with text
                    const bubbleRadius = 20;
                    const bubblePadding = 10;
                    const textWidth = ctx.measureText(anno.text || '').width;
                    const bubbleWidth = textWidth + (bubblePadding * 2);
                    const bubbleHeight = 30 + (bubblePadding * 2);

                    // Draw the pointer line
                    ctx.beginPath();
                    ctx.moveTo(anno.x1, anno.y1); // Start point (where user clicked)
                    ctx.lineTo(anno.x2, anno.y2); // End point (bubble center)
                    ctx.stroke();

                    // Draw the bubble
                    ctx.save();
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                    ctx.strokeStyle = anno.color;

                    // Draw rounded rectangle
                    ctx.beginPath();
                    ctx.moveTo(anno.x2 - bubbleWidth/2 + bubbleRadius, anno.y2 - bubbleHeight/2);
                    ctx.lineTo(anno.x2 + bubbleWidth/2 - bubbleRadius, anno.y2 - bubbleHeight/2);
                    ctx.quadraticCurveTo(anno.x2 + bubbleWidth/2, anno.y2 - bubbleHeight/2, anno.x2 + bubbleWidth/2, anno.y2 - bubbleHeight/2 + bubbleRadius);
                    ctx.lineTo(anno.x2 + bubbleWidth/2, anno.y2 + bubbleHeight/2 - bubbleRadius);
                    ctx.quadraticCurveTo(anno.x2 + bubbleWidth/2, anno.y2 + bubbleHeight/2, anno.x2 + bubbleWidth/2 - bubbleRadius, anno.y2 + bubbleHeight/2);
                    ctx.lineTo(anno.x2 - bubbleWidth/2 + bubbleRadius, anno.y2 + bubbleHeight/2);
                    ctx.quadraticCurveTo(anno.x2 - bubbleWidth/2, anno.y2 + bubbleHeight/2, anno.x2 - bubbleWidth/2, anno.y2 + bubbleHeight/2 - bubbleRadius);
                    ctx.lineTo(anno.x2 - bubbleWidth/2, anno.y2 - bubbleHeight/2 + bubbleRadius);
                    ctx.quadraticCurveTo(anno.x2 - bubbleWidth/2, anno.y2 - bubbleHeight/2, anno.x2 - bubbleWidth/2 + bubbleRadius, anno.y2 - bubbleHeight/2);
                    ctx.closePath();
                    ctx.fill();
                    ctx.stroke();

                    // Draw the text
                    ctx.fillStyle = anno.color;
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText(anno.text || '', anno.x2, anno.y2);
                    ctx.restore();
                    break;

                case 'blur':
                    // Apply a blur effect to a rectangular area
                    try {
                        // Get the area to blur
                        const blurWidth = Math.abs(anno.x2 - anno.x1);
                        const blurHeight = Math.abs(anno.y2 - anno.y1);
                        const blurX = Math.min(anno.x1, anno.x2);
                        const blurY = Math.min(anno.y1, anno.y2);

                        if (blurWidth < 5 || blurHeight < 5) break; // Skip if too small

                        // Get the image data for the area
                        const imageData = ctx.getImageData(blurX, blurY, blurWidth, blurHeight);

                        // Apply a simple box blur
                        const radius = anno.radius || blurRadius;
                        const pixels = imageData.data;
                        const width = imageData.width;
                        const height = imageData.height;

                        // Create a temporary array to store the blurred result
                        const tempPixels = new Uint8ClampedArray(pixels.length);

                        // Apply blur (simple box blur algorithm)
                        for (let y = 0; y < height; y++) {
                            for (let x = 0; x < width; x++) {
                                let r = 0, g = 0, b = 0, a = 0, count = 0;

                                // Sample the surrounding pixels
                                for (let ky = -radius; ky <= radius; ky++) {
                                    for (let kx = -radius; kx <= radius; kx++) {
                                        const posX = Math.min(width - 1, Math.max(0, x + kx));
                                        const posY = Math.min(height - 1, Math.max(0, y + ky));
                                        const offset = (posY * width + posX) * 4;

                                        r += pixels[offset];
                                        g += pixels[offset + 1];
                                        b += pixels[offset + 2];
                                        a += pixels[offset + 3];
                                        count++;
                                    }
                                }

                                // Calculate average and set in temp array
                                const offset = (y * width + x) * 4;
                                tempPixels[offset] = r / count;
                                tempPixels[offset + 1] = g / count;
                                tempPixels[offset + 2] = b / count;
                                tempPixels[offset + 3] = a / count;
                            }
                        }

                        // Copy the temp pixels back to the image data
                        for (let i = 0; i < pixels.length; i++) {
                            pixels[i] = tempPixels[i];
                        }

                        // Put the blurred image data back
                        ctx.putImageData(imageData, blurX, blurY);
                    } catch (blurError) {
                        console.error("Error applying blur effect:", blurError);
                    }
                    break;
            }
        } catch (drawError) {
             console.error(`Error drawing annotation type ${anno.type} at index ${index}:`, drawError, anno);
        }
    });
    // Reset global styles
    ctx.lineWidth = 1;
    ctx.strokeStyle = '#000000';
    ctx.fillStyle = '#000000';
    ctx.lineCap = 'butt';
    ctx.lineJoin = 'miter';
    ctx.shadowColor = 'transparent'; ctx.shadowBlur = 0; ctx.shadowOffsetX = 0; ctx.shadowOffsetY = 0;
}

/** Helper: Adds a temporary text input overlay for annotation (using physical coords for placement) */
function addTextInputOverlay(physicalX, physicalY) {
    // Ensure previous overlays are removed
    screenshotCanvas?.parentElement?.querySelectorAll('input.Stickara-temp-text-input').forEach(el => el.remove());

    // Get the canvas container's position and dimensions
    const containerRect = screenshotCanvas.parentElement?.getBoundingClientRect();

    if (!containerRect) {
        console.error("Stickara: Cannot position text input - canvas container not found");
        return;
    }

    const inputOverlay = document.createElement('input');
    inputOverlay.type = 'text';
    inputOverlay.className = 'Stickara-temp-text-input';
    inputOverlay.style.position = 'absolute';

    // Position the overlay using logical pixels, derived from physical coords
    const dpr = window.devicePixelRatio || 1;

    // Calculate position relative to the container
    inputOverlay.style.left = `${physicalX / dpr}px`;
    inputOverlay.style.top = `${physicalY / dpr}px`;

    // Style the input itself (these are CSS pixels)
    inputOverlay.style.border = '1px dashed #555';
    inputOverlay.style.padding = '4px';
    inputOverlay.style.fontSize = '14px'; // This size is independent of DPR scaling
    inputOverlay.style.fontFamily = 'sans-serif';
    inputOverlay.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
    inputOverlay.style.zIndex = '11000'; // Increased z-index to ensure visibility
    inputOverlay.style.minWidth = '80px';
    inputOverlay.style.color = annotationColor;
    inputOverlay.style.boxSizing = 'border-box';
    inputOverlay.placeholder = 'Type text here...';

    // Make sure the container has position relative or absolute for proper positioning
    if (screenshotCanvas.parentElement) {
        const containerPosition = window.getComputedStyle(screenshotCanvas.parentElement).position;
        if (containerPosition === 'static') {
            screenshotCanvas.parentElement.style.position = 'relative';
        }
    }

    screenshotCanvas.parentElement?.appendChild(inputOverlay);

    // Ensure the input is visible and focused
    setTimeout(() => {
        inputOverlay.focus();
    }, 50);

    const finalizeText = () => {
        const text = inputOverlay.value.trim();
        if (text) {
            annotations.push({
                type: 'text',
                text: text,
                color: annotationColor,
                lineWidth: annotationLineWidth, // Store physical line width
                x1: physicalX, y1: physicalY // Store physical coordinates
            });
            redrawScreenshotCanvas();
        }
        try { inputOverlay.remove(); } catch (e) {}
    };

    inputOverlay.addEventListener('blur', finalizeText);
    inputOverlay.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            finalizeText();
        } else if (e.key === 'Escape') {
            inputOverlay.value = '';
            finalizeText();
            screenshotCanvas?.focus();
        }
    });
}

/**
 * Calculates optimal scaling for screenshot display based on image dimensions and type
 * @param {number} imgWidth - Original image width in pixels
 * @param {number} imgHeight - Original image height in pixels
 * @param {number} dpr - Device pixel ratio
 * @returns {Object} - {logicalWidth, logicalHeight, scale, screenshotType}
 */
function calculateOptimalScreenshotScale(imgWidth, imgHeight, dpr) {
    // Calculate logical dimensions from physical pixels
    const originalLogicalWidth = imgWidth / dpr;
    const originalLogicalHeight = imgHeight / dpr;

    // Determine screenshot type based on aspect ratio and dimensions
    const aspectRatio = imgWidth / imgHeight;
    const isVeryTall = aspectRatio < 0.5; // Height is more than 2x width
    const isFullPage = imgHeight > (imgWidth * 2); // Height is more than 2x width (absolute)
    const isLargeImage = imgWidth > 2000 || imgHeight > 2000; // Large dimensions

    // Viewport constraints
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const maxModalWidth = viewportWidth * 0.9;
    const maxModalHeight = viewportHeight * 0.9;

    // Available space for canvas (accounting for modal padding and toolbar)
    const modalPadding = 40;
    const toolbarHeight = 80;
    const maxCanvasWidth = maxModalWidth - modalPadding;
    const maxCanvasHeight = maxModalHeight - toolbarHeight - modalPadding;

    let finalLogicalWidth, finalLogicalHeight, scale, screenshotType;

    if (isFullPage || isVeryTall) {
        // Full-page screenshot: Optimize for readability and annotation precision
        screenshotType = 'full-page';

        // For full-page screenshots, prioritize width-based scaling for readability
        // Users typically need to see text and details clearly, not the entire height at once

        // Calculate scale based on width to ensure text is readable
        const widthScale = Math.min(maxCanvasWidth / originalLogicalWidth, 1.5); // Allow up to 150% for small images

        // For very tall images, use a higher minimum scale to maintain quality
        const minScale = Math.max(0.8, Math.min(1.0, maxCanvasWidth / originalLogicalWidth));

        // Choose the larger of width-based scale and minimum scale
        scale = Math.max(widthScale, minScale);

        // Ensure we don't go below a quality threshold
        scale = Math.max(scale, 0.75); // Never scale below 75% for quality

        // Apply scale
        finalLogicalWidth = Math.round(originalLogicalWidth * scale);
        finalLogicalHeight = Math.round(originalLogicalHeight * scale);

        // Ensure width doesn't exceed canvas bounds
        if (finalLogicalWidth > maxCanvasWidth) {
            const adjustmentRatio = maxCanvasWidth / finalLogicalWidth;
            finalLogicalWidth = maxCanvasWidth;
            finalLogicalHeight = Math.round(finalLogicalHeight * adjustmentRatio);
            scale *= adjustmentRatio;
        }

    } else if (isLargeImage) {
        // Large regular screenshot: Scale to fit while maintaining quality
        screenshotType = 'large-viewport';

        // Scale to fit within canvas bounds while maintaining aspect ratio
        const widthRatio = maxCanvasWidth / originalLogicalWidth;
        const heightRatio = maxCanvasHeight / originalLogicalHeight;
        scale = Math.min(widthRatio, heightRatio, 1); // Don't scale up

        finalLogicalWidth = Math.round(originalLogicalWidth * scale);
        finalLogicalHeight = Math.round(originalLogicalHeight * scale);

    } else {
        // Regular viewport screenshot: Use original size or slight scaling
        screenshotType = 'viewport';

        // For regular screenshots, prefer original size unless too large
        const widthRatio = maxCanvasWidth / originalLogicalWidth;
        const heightRatio = maxCanvasHeight / originalLogicalHeight;
        scale = Math.min(widthRatio, heightRatio, 1.1); // Allow slight upscaling for small images

        finalLogicalWidth = Math.round(originalLogicalWidth * scale);
        finalLogicalHeight = Math.round(originalLogicalHeight * scale);
    }

    // Ensure minimum usable size
    const minWidth = 300;
    const minHeight = 200;

    if (finalLogicalWidth < minWidth) {
        const adjustmentRatio = minWidth / finalLogicalWidth;
        finalLogicalWidth = minWidth;
        finalLogicalHeight = Math.round(finalLogicalHeight * adjustmentRatio);
        scale *= adjustmentRatio;
    }

    if (finalLogicalHeight < minHeight) {
        const adjustmentRatio = minHeight / finalLogicalHeight;
        finalLogicalHeight = minHeight;
        finalLogicalWidth = Math.round(finalLogicalWidth * adjustmentRatio);
        scale *= adjustmentRatio;
    }

    return {
        logicalWidth: finalLogicalWidth,
        logicalHeight: finalLogicalHeight,
        scale: scale,
        screenshotType: screenshotType,
        originalLogicalWidth: originalLogicalWidth,
        originalLogicalHeight: originalLogicalHeight
    };
}

/**
 * Calculates optimal modal size based on screenshot scaling result
 * @param {Object} scalingResult - Result from calculateOptimalScreenshotScale
 * @param {number} canvasLogicalWidth - Canvas logical width
 * @param {number} canvasLogicalHeight - Canvas logical height
 * @returns {Object} - {width, height, canvasMaxWidth, canvasMaxHeight}
 */
function calculateModalSize(scalingResult, canvasLogicalWidth, canvasLogicalHeight) {
    const modalPadding = 40;
    const toolbarHeight = 80;
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Maximum modal dimensions
    const maxModalWidth = viewportWidth * 0.95; // Slightly larger for full-page screenshots
    const maxModalHeight = viewportHeight * 0.95;

    let modalWidth, modalHeight, canvasMaxWidth, canvasMaxHeight;

    if (scalingResult.screenshotType === 'full-page') {
        // For full-page screenshots, optimize for scrolling workflow

        // Modal should be large enough to show canvas at good size but allow scrolling
        modalWidth = Math.min(canvasLogicalWidth + modalPadding, maxModalWidth);
        modalHeight = Math.min(viewportHeight * 0.85, maxModalHeight); // Fixed height for consistent UX

        // Canvas container should allow scrolling for tall content
        canvasMaxWidth = modalWidth - modalPadding;
        canvasMaxHeight = modalHeight - toolbarHeight - modalPadding;

        // Ensure canvas container is large enough to be useful but not overwhelming
        canvasMaxHeight = Math.max(canvasMaxHeight, 400); // Minimum useful height

    } else if (scalingResult.screenshotType === 'large-viewport') {
        // For large viewport screenshots, try to fit more content

        modalWidth = Math.min(canvasLogicalWidth + modalPadding, maxModalWidth);
        modalHeight = Math.min(canvasLogicalHeight + toolbarHeight + modalPadding, maxModalHeight);

        canvasMaxWidth = modalWidth - modalPadding;
        canvasMaxHeight = modalHeight - toolbarHeight - modalPadding;

    } else {
        // For regular screenshots, size modal to fit content

        modalWidth = Math.min(canvasLogicalWidth + modalPadding, maxModalWidth);
        modalHeight = Math.min(canvasLogicalHeight + toolbarHeight + modalPadding, maxModalHeight);

        canvasMaxWidth = modalWidth - modalPadding;
        canvasMaxHeight = modalHeight - toolbarHeight - modalPadding;
    }

    // Ensure minimum modal size
    modalWidth = Math.max(modalWidth, 450);
    modalHeight = Math.max(modalHeight, 350);

    // Recalculate canvas max dimensions based on final modal size
    canvasMaxWidth = Math.max(canvasMaxWidth, 350);
    canvasMaxHeight = Math.max(canvasMaxHeight, 250);

    return {
        width: Math.round(modalWidth),
        height: Math.round(modalHeight),
        canvasMaxWidth: Math.round(canvasMaxWidth),
        canvasMaxHeight: Math.round(canvasMaxHeight)
    };
}

/**
 * Extracts data URL from worker result, handling various result structures
 * @param {any} result - Worker result
 * @param {string} expectedProperty - Expected property name (e.g., 'processedDataUrl', 'convertedDataUrl')
 * @param {string} fallbackDataUrl - Fallback data URL to use if extraction fails
 * @returns {string} - Extracted or fallback data URL
 */
function extractDataUrlFromWorkerResult(result, expectedProperty, fallbackDataUrl) {
    // Case 1: Direct property access (normal worker result)
    if (result && result[expectedProperty]) {
        return result[expectedProperty];
    }

    // Case 2: Direct data URL string
    if (result && typeof result === 'string' && result.startsWith('data:')) {
        return result;
    }

    // Case 3: Nested result structure (worker manager fallback)
    if (result && result.result && result.result[expectedProperty]) {
        return result.result[expectedProperty];
    }

    // Case 4: Worker manager fallback structure with data property
    if (result && result.success && result.data && result.data.imageDataUrl) {
        return result.data.imageDataUrl;
    }

    // Case 4b: Handle successful result with null data (worker processed but failed)
    if (result && result.success && result.data === null) {
        return fallbackDataUrl;
    }

    // Case 5: Check for common alternative property names
    const alternativeProperties = ['dataUrl', 'imageDataUrl', 'url', 'data'];
    for (const prop of alternativeProperties) {
        if (result && result[prop] && typeof result[prop] === 'string' && result[prop].startsWith('data:image/')) {
            return result[prop];
        }
    }

    // Case 6: Deep search for any property that looks like a data URL
    if (result && typeof result === 'object') {
        const searchObject = (obj, path = '') => {
            for (const [key, value] of Object.entries(obj)) {
                const currentPath = path ? `${path}.${key}` : key;
                if (typeof value === 'string' && value.startsWith('data:image/')) {
                    return value;
                } else if (typeof value === 'object' && value !== null && path.split('.').length < 3) {
                    // Recursively search nested objects (limit depth to prevent infinite loops)
                    const found = searchObject(value, currentPath);
                    if (found) return found;
                }
            }
            return null;
        };

        const foundDataUrl = searchObject(result);
        if (foundDataUrl) return foundDataUrl;
    }

    // Case 7: Fallback - use original image
    return fallbackDataUrl;
}

/**
 * Starts dragging the screenshot modal
 * @param {MouseEvent} e - Mouse event
 */
function startModalDrag(e) {
    e.preventDefault();
    e.stopPropagation();

    isModalDragging = true;
    modalDragStartX = e.clientX;
    modalDragStartY = e.clientY;

    // Get current modal position
    const rect = screenshotAnnotationModal.getBoundingClientRect();
    modalInitialLeft = rect.left;
    modalInitialTop = rect.top;

    // Set modal to absolute positioning if not already
    if (screenshotAnnotationModal.style.position !== 'absolute') {
        screenshotAnnotationModal.style.position = 'absolute';
        screenshotAnnotationModal.style.left = modalInitialLeft + 'px';
        screenshotAnnotationModal.style.top = modalInitialTop + 'px';
        screenshotAnnotationModal.style.transform = 'none'; // Remove any centering transform
    }

    // Add global event listeners
    document.addEventListener('mousemove', dragModal);
    document.addEventListener('mouseup', stopModalDrag);

    // Change cursor
    document.body.style.cursor = 'grabbing';
}

/**
 * Handles modal dragging
 * @param {MouseEvent} e - Mouse event
 */
function dragModal(e) {
    if (!isModalDragging) return;

    e.preventDefault();

    const deltaX = e.clientX - modalDragStartX;
    const deltaY = e.clientY - modalDragStartY;

    let newLeft = modalInitialLeft + deltaX;
    let newTop = modalInitialTop + deltaY;

    // Constrain to viewport
    const modalRect = screenshotAnnotationModal.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Ensure modal stays within viewport bounds
    newLeft = Math.max(0, Math.min(newLeft, viewportWidth - modalRect.width));
    newTop = Math.max(0, Math.min(newTop, viewportHeight - modalRect.height));

    screenshotAnnotationModal.style.left = newLeft + 'px';
    screenshotAnnotationModal.style.top = newTop + 'px';
}

/**
 * Stops dragging the screenshot modal
 */
function stopModalDrag() {
    if (!isModalDragging) return;

    isModalDragging = false;

    // Remove global event listeners
    document.removeEventListener('mousemove', dragModal);
    document.removeEventListener('mouseup', stopModalDrag);

    // Reset cursor
    document.body.style.cursor = '';
}

/** Creates the screenshot annotation modal UI, including the new Download button */
function createScreenshotAnnotationModal() {
    if (document.getElementById(SCREENSHOT_MODAL_ID)) return;

    // Ensure createToolButton is available
    if (typeof createToolButton !== 'function') {
        console.error("Stickara Error: createToolButton function not found. Cannot create annotation modal.");
        return;
    }

    screenshotAnnotationModal = document.createElement('div');
    screenshotAnnotationModal.id = SCREENSHOT_MODAL_ID;
    // Apply styles via CSS using the ID or a dedicated class
    // Use a distinct class if needed, or reuse 'Stickara-diagram-editor' if styles match well
    screenshotAnnotationModal.classList.add('Stickara-screenshot-modal-custom', 'Stickara-diagram-editor'); // Example using both
    screenshotAnnotationModal.style.display = 'none'; // Hidden initially
    screenshotAnnotationModal.style.width = 'auto'; // Auto size initially
    screenshotAnnotationModal.style.height = 'auto';
    screenshotAnnotationModal.style.maxWidth = '90vw'; // Limit max size
    screenshotAnnotationModal.style.maxHeight = '90vh';
    screenshotAnnotationModal.setAttribute('role', 'dialog');
    screenshotAnnotationModal.setAttribute('aria-modal', 'true');
    screenshotAnnotationModal.setAttribute('aria-labelledby', 'Stickara-screenshot-title');

    const modalTitle = document.createElement('h4');
    modalTitle.id = 'Stickara-screenshot-title';
    modalTitle.textContent = 'Annotate Screenshot';
    modalTitle.style.cursor = 'move'; // Indicate draggable title
    modalTitle.style.userSelect = 'none'; // Prevent text selection during drag
    modalTitle.style.padding = '10px';
    modalTitle.style.margin = '0';
    modalTitle.style.backgroundColor = '#f0f0f0';
    modalTitle.style.borderBottom = '1px solid #ccc';

    // Add drag functionality to modal title
    modalTitle.addEventListener('mousedown', startModalDrag);

    // --- Toolbar ---
    const toolbar = document.createElement('div');
    toolbar.id = SCREENSHOT_TOOLBAR_ID;
    toolbar.classList.add('Stickara-diagram-toolbar'); // Reuse existing style

    const toolsLeft = document.createElement('div'); // For drawing tools, color, width
    toolsLeft.style.display = 'flex'; toolsLeft.style.alignItems = 'center'; toolsLeft.style.gap = '5px'; toolsLeft.style.flexWrap = 'wrap'; // Allow wrapping
    const toolsRight = document.createElement('div'); // For actions (clear, save, cancel, download)
    toolsRight.style.display = 'flex'; toolsRight.style.alignItems = 'center'; toolsRight.style.gap = '5px';

    // --- Enhanced Annotation Tool Buttons ---

    // Basic Drawing Tools
    const arrowBtn = createToolButton('↗️', '', 'Arrow Tool', () => {
        currentAnnotationTool = 'arrow';
        updateActiveAnnotationTool(arrowBtn);
    });
    arrowBtn.dataset.tool = 'arrow';

    const penBtn = createToolButton('✍️', '', 'Freehand Pen', () => {
        currentAnnotationTool = 'pen';
        updateActiveAnnotationTool(penBtn);
    });
    penBtn.dataset.tool = 'pen';

    const brushBtn = createToolButton('🖌️', '', 'Brush Tool', () => {
        currentAnnotationTool = 'brush';
        updateActiveAnnotationTool(brushBtn);
    });
    brushBtn.dataset.tool = 'brush';

    const lineBtn = createToolButton('➖', '', 'Line Tool', () => {
        currentAnnotationTool = 'line';
        updateActiveAnnotationTool(lineBtn);
    });
    lineBtn.dataset.tool = 'line';

    const rectBtn = createToolButton('⬜', '', 'Rectangle Tool', () => {
        currentAnnotationTool = 'rect';
        updateActiveAnnotationTool(rectBtn);
    });
    rectBtn.dataset.tool = 'rect';

    const circleBtn = createToolButton('⚪', '', 'Circle Tool', () => {
        currentAnnotationTool = 'circle';
        updateActiveAnnotationTool(circleBtn);
    });
    circleBtn.dataset.tool = 'circle';

    // Advanced Shape Tools
    const triangleBtn = createToolButton('🔺', '', 'Triangle Tool', () => { currentAnnotationTool = 'triangle'; updateActiveAnnotationTool(triangleBtn); }); triangleBtn.dataset.tool = 'triangle';
    const diamondBtn = createToolButton('🔷', '', 'Diamond Tool', () => { currentAnnotationTool = 'diamond'; updateActiveAnnotationTool(diamondBtn); }); diamondBtn.dataset.tool = 'diamond';
    const starBtn = createToolButton('⭐', '', 'Star Tool', () => { currentAnnotationTool = 'star'; updateActiveAnnotationTool(starBtn); }); starBtn.dataset.tool = 'star';
    const polygonBtn = createToolButton('⬡', '', 'Polygon Tool', () => { currentAnnotationTool = 'polygon'; updateActiveAnnotationTool(polygonBtn); }); polygonBtn.dataset.tool = 'polygon';

    // Professional Tools
    const connectorBtn = createToolButton('🔗', '', 'Connector Line', () => { currentAnnotationTool = 'connector'; updateActiveAnnotationTool(connectorBtn); }); connectorBtn.dataset.tool = 'connector';
    const measurementBtn = createToolButton('📏', '', 'Measurement Tool', () => { currentAnnotationTool = 'measurement'; updateActiveAnnotationTool(measurementBtn); }); measurementBtn.dataset.tool = 'measurement';
    const eraserBtn = createToolButton('🧽', '', 'Eraser Tool', () => { isEraserMode = !isEraserMode; updateEraserMode(eraserBtn); }); eraserBtn.dataset.tool = 'eraser';

    // Text and Annotation Tools
    const textBtn = createToolButton('🔤', 'Aa', 'Text Tool', () => {
        currentAnnotationTool = 'text';
        updateActiveAnnotationTool(textBtn);
    });
    textBtn.dataset.tool = 'text';
    textBtn.style.fontWeight = 'bold';
    textBtn.style.fontSize = '14px';

    const highlightBtn = createToolButton('🖍️', '', 'Highlight Tool', () => {
        currentAnnotationTool = 'highlight';
        updateActiveAnnotationTool(highlightBtn);
    });
    highlightBtn.dataset.tool = 'highlight';

    const calloutBtn = createToolButton('💬', '', 'Callout Bubble', () => {
        currentAnnotationTool = 'callout';
        updateActiveAnnotationTool(calloutBtn);
    });
    calloutBtn.dataset.tool = 'callout';

    const blurBtn = createToolButton('🔍', '', 'Blur Tool (for privacy)', () => {
        currentAnnotationTool = 'blur';
        updateActiveAnnotationTool(blurBtn);
    });
    blurBtn.dataset.tool = 'blur';

    // History Tools
    const undoBtn = createToolButton('↶', '', 'Undo (Ctrl+Z)', performUndo); undoBtn.dataset.tool = 'undo';
    const redoBtn = createToolButton('↷', '', 'Redo (Ctrl+Y)', performRedo); redoBtn.dataset.tool = 'redo';

    // Add essential tools in organized groups
    toolsLeft.appendChild(arrowBtn);
    toolsLeft.appendChild(penBtn);
    toolsLeft.appendChild(brushBtn);
    toolsLeft.appendChild(eraserBtn);

    // Add separator
    const separator1 = document.createElement('div');
    separator1.style.width = '1px';
    separator1.style.height = '24px';
    separator1.style.backgroundColor = '#ccc';
    separator1.style.margin = '0 4px';
    toolsLeft.appendChild(separator1);

    toolsLeft.appendChild(lineBtn);
    toolsLeft.appendChild(rectBtn);
    toolsLeft.appendChild(circleBtn);
    toolsLeft.appendChild(triangleBtn);
    toolsLeft.appendChild(diamondBtn);

    // Add separator
    const separator2 = document.createElement('div');
    separator2.style.width = '1px';
    separator2.style.height = '24px';
    separator2.style.backgroundColor = '#ccc';
    separator2.style.margin = '0 4px';
    toolsLeft.appendChild(separator2);

    toolsLeft.appendChild(textBtn);
    toolsLeft.appendChild(highlightBtn);
    toolsLeft.appendChild(measurementBtn);
    toolsLeft.appendChild(blurBtn);

    // Add separator
    const separator3 = document.createElement('div');
    separator3.style.width = '1px';
    separator3.style.height = '24px';
    separator3.style.backgroundColor = '#ccc';
    separator3.style.margin = '0 4px';
    toolsLeft.appendChild(separator3);

    toolsLeft.appendChild(undoBtn);
    toolsLeft.appendChild(redoBtn);

    // --- Color Palette ---
    const colorPalette = document.createElement('div');
    colorPalette.style.display = 'flex';
    colorPalette.style.gap = '3px';
    colorPalette.style.borderLeft = '1px solid #ccc';
    colorPalette.style.marginLeft = '6px';
    colorPalette.style.paddingLeft = '6px';

    ANNOTATION_COLORS.forEach(color => {
        const swatch = document.createElement('button');

        // Force color display with important styles
        swatch.style.cssText = `
            background-color: ${color} !important;
            width: 20px !important;
            height: 20px !important;
            border-radius: 3px !important;
            border: ${color === '#FFFFFF' ? '2px solid #333' : '2px solid #fff'} !important;
            padding: 0 !important;
            margin: 0 1px !important;
            cursor: pointer !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3) !important;
            transition: all 0.2s ease !important;
            min-width: 20px !important;
        `;

        swatch.dataset.color = color;
        swatch.title = `Set color to ${color}`;

        // Mark initial active color
        if (color === annotationColor) {
            swatch.style.transform = 'scale(1.2)';
            swatch.style.boxShadow = '0 0 0 2px #2196f3, 0 2px 6px rgba(0,0,0,0.4) !important';
        }

        swatch.addEventListener('click', (e) => {
            annotationColor = e.target.dataset.color;
            // Update active state visually
            colorPalette.querySelectorAll('button').forEach(btn => {
                btn.style.transform = 'scale(1)';
                btn.style.boxShadow = '0 1px 3px rgba(0,0,0,0.3) !important';
            });
            e.target.style.transform = 'scale(1.2)';
            e.target.style.boxShadow = '0 0 0 2px #2196f3, 0 2px 6px rgba(0,0,0,0.4) !important';

            // Update color of active text input overlay if it exists
            const activeTextInput = screenshotCanvas?.parentElement?.querySelector('input.Stickara-temp-text-input');
            if (activeTextInput) activeTextInput.style.color = annotationColor;
        });

        colorPalette.appendChild(swatch);
    });
    toolsLeft.appendChild(colorPalette);

    // --- Compact Line Width Slider ---
    const lineWidthContainer = document.createElement('div');
    lineWidthContainer.style.display = 'flex';
    lineWidthContainer.style.alignItems = 'center';
    lineWidthContainer.style.marginLeft = '6px';

    const lineWidthSlider = document.createElement('input');
    lineWidthSlider.type = 'range';
    lineWidthSlider.min = 1;
    lineWidthSlider.max = 10; // Reduced range for compactness
    lineWidthSlider.value = annotationLineWidth;
    lineWidthSlider.title = `Line Width: ${annotationLineWidth}`;
    lineWidthSlider.style.width = '50px'; // More compact
    lineWidthSlider.style.height = '20px';

    const lineWidthLabel = document.createElement('span');
    lineWidthLabel.textContent = annotationLineWidth;
    lineWidthLabel.style.fontSize = '11px';
    lineWidthLabel.style.marginLeft = '3px';
    lineWidthLabel.style.minWidth = '15px';
    lineWidthLabel.style.textAlign = 'center';
    lineWidthLabel.style.color = '#666';

    lineWidthSlider.addEventListener('input', (e) => {
        annotationLineWidth = parseInt(e.target.value, 10);
        lineWidthSlider.title = `Line Width: ${annotationLineWidth}`;
        lineWidthLabel.textContent = annotationLineWidth;
    });

    lineWidthContainer.appendChild(lineWidthSlider);
    lineWidthContainer.appendChild(lineWidthLabel);
    toolsLeft.appendChild(lineWidthContainer);

    // --- Action Buttons (Right Side) ---
    const clearAnnotationsBtn = createToolButton('🗑️', '', 'Clear All Annotations', () => {
         if(annotations.length > 0 && confirm("Clear all annotations from this screenshot? This cannot be undone.")) {
             annotations = []; // Clear the annotations array
             redrawScreenshotCanvas(); // Redraw the canvas without annotations
         }
    });
    const saveBtn = createToolButton('💾', '', 'Save Screenshot', handleSaveScreenshot);
    const insertBtn = createToolButton('📝', '', 'Insert into Note', handleInsertScreenshotIntoNote); // New insert button
    const downloadBtn = createToolButton('⬇️', '', 'Download Annotated Screenshot', handleDownloadAnnotatedScreenshot);
    const cancelBtn = createToolButton('❌', '', 'Cancel Annotation', closeScreenshotAnnotationModal);

    // Add buttons to the right toolbar section
    toolsRight.appendChild(clearAnnotationsBtn);
    toolsRight.appendChild(downloadBtn);
    toolsRight.appendChild(saveBtn);    // Save button
    toolsRight.appendChild(insertBtn);  // Insert into Note button
    toolsRight.appendChild(cancelBtn);

    // Assemble the toolbar
    toolbar.appendChild(toolsLeft);
    toolbar.appendChild(toolsRight);

    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if (!screenshotAnnotationModal || screenshotAnnotationModal.style.display === 'none') return;

        // Prevent default for our shortcuts
        if ((e.ctrlKey || e.metaKey) && ['z', 'y', 'a', 's', 'i'].includes(e.key.toLowerCase())) {
            e.preventDefault();
        }

        if (e.ctrlKey || e.metaKey) {
            switch (e.key.toLowerCase()) {
                case 'z':
                    if (e.shiftKey) {
                        performRedo();
                    } else {
                        performUndo();
                    }
                    break;
                case 'y':
                    performRedo();
                    break;
                case 'a':
                    // Select all annotations (future feature)
                    break;
                case 's':
                    // Save shortcut
                    handleSaveScreenshot();
                    break;
                case 'i':
                    // Insert into note shortcut
                    handleInsertScreenshotIntoNote();
                    break;
            }
        }

        // Tool shortcuts
        switch (e.key.toLowerCase()) {
            case 'a':
                if (!e.ctrlKey && !e.metaKey) {
                    currentAnnotationTool = 'arrow';
                    updateActiveAnnotationTool(arrowBtn);
                }
                break;
            case 'p':
                currentAnnotationTool = 'pen';
                updateActiveAnnotationTool(penBtn);
                break;
            case 'b':
                currentAnnotationTool = 'brush';
                updateActiveAnnotationTool(brushBtn);
                break;
            case 'l':
                currentAnnotationTool = 'line';
                updateActiveAnnotationTool(lineBtn);
                break;
            case 'r':
                currentAnnotationTool = 'rect';
                updateActiveAnnotationTool(rectBtn);
                break;
            case 'c':
                currentAnnotationTool = 'circle';
                updateActiveAnnotationTool(circleBtn);
                break;
            case 't':
                currentAnnotationTool = 'text';
                updateActiveAnnotationTool(textBtn);
                break;
            case 'h':
                currentAnnotationTool = 'highlight';
                updateActiveAnnotationTool(highlightBtn);
                break;
            case 'e':
                isEraserMode = !isEraserMode;
                updateEraserMode(eraserBtn);
                break;
            case '1':
                currentAnnotationTool = 'triangle';
                updateActiveAnnotationTool(triangleBtn);
                break;
            case '2':
                currentAnnotationTool = 'diamond';
                updateActiveAnnotationTool(diamondBtn);
                break;
            case 'm':
                currentAnnotationTool = 'measurement';
                updateActiveAnnotationTool(measurementBtn);
                break;
            case 'escape':
                closeScreenshotAnnotationModal();
                break;
        }
    });

    // --- Canvas Container ---
    canvasContainer = document.createElement('div'); // Use global reference
    canvasContainer.style.flexGrow = '1'; // Take available vertical space
    canvasContainer.style.overflow = 'auto'; // Enable scrolling for large screenshots
    canvasContainer.style.border = '1px solid #ccc';
    canvasContainer.style.backgroundColor = '#f0f0f0'; // Light background for the container
    canvasContainer.style.position = 'relative'; // Needed for absolute positioning of text input
    canvasContainer.id = 'Stickara-screenshot-canvas-container'; // Add ID for easier targeting

    // Initial sizing - will be updated when image loads based on screenshot type
    canvasContainer.style.width = '100%';
    canvasContainer.style.height = '100%';

    // Apply user's preferred scrollbar styling (invisible by default, visible on hover)
    canvasContainer.style.scrollbarWidth = 'thin'; // Firefox
    canvasContainer.style.scrollbarColor = 'transparent transparent'; // Firefox - invisible by default

    // Create canvas with enhanced error handling for YouTube compatibility
    screenshotCanvas = createScreenshotCanvas();
    if (!screenshotCanvas) {
        console.error("Stickara: Failed to create screenshot canvas");
        alert("Error: Could not create screenshot canvas. Please try again.");
        return;
    }
    canvasContainer.appendChild(screenshotCanvas);

    // --- Add Canvas Event Listeners ---
    screenshotCanvas.addEventListener('mousedown', (e) => {
         if (e.target !== screenshotCanvas) return;

         // Use scroll-aware coordinates
         const coords = getScrollAwareCanvasCoordinates(e);
         const physicalX = coords.x;
         const physicalY = coords.y;

         // Handle eraser mode - must be before other tool handling
         if (isEraserMode) {
             e.preventDefault();
             if (handleEraserClick(physicalX, physicalY)) {
                 return; // Annotation was erased
             }
             // If no annotation was erased, don't start drawing
             return;
         }

         // Check if clicking on an existing annotation for selection/dragging
         let clickedAnnotation = null;
         for (let i = annotations.length - 1; i >= 0; i--) {
             if (hitTestAnnotation(physicalX, physicalY, annotations[i])) {
                 clickedAnnotation = annotations[i];
                 break;
             }
         }

         // If clicked on an annotation, start dragging
         if (clickedAnnotation && currentAnnotationTool === 'arrow') {
             selectedAnnotation = clickedAnnotation;
             isDraggingAnnotation = true;
             annotationDragStartX = physicalX;
             annotationDragStartY = physicalY;

             // Calculate offset from annotation center for smooth dragging
             const bounds = getAnnotationBounds(clickedAnnotation);
             if (bounds) {
                 annotationDragOffsetX = physicalX - (bounds.left + bounds.width / 2);
                 annotationDragOffsetY = physicalY - (bounds.top + bounds.height / 2);
             } else {
                 annotationDragOffsetX = 0;
                 annotationDragOffsetY = 0;
             }

             redrawScreenshotCanvas();
             return;
         }

         // Clear selection if clicking elsewhere
         selectedAnnotation = null;

        if (currentAnnotationTool === 'text') {
            addTextInputOverlay(physicalX, physicalY);
            return;
        } else if (currentAnnotationTool === 'callout') {
            // For callout, we need to get text first, then handle the positioning
            const calloutText = prompt('Enter callout text:', '');
            if (calloutText === null) return; // User cancelled

            // Store the callout with initial position
            annotations.push({
                type: 'callout',
                text: calloutText,
                color: annotationColor,
                lineWidth: annotationLineWidth,
                x1: physicalX, y1: physicalY, // Start point (where user clicked)
                x2: physicalX + 100, y2: physicalY - 50 // Default bubble position
            });

            redrawScreenshotCanvas();
            return;
        }

        isAnnotating = true;
        annotationStartX = physicalX; // Store physical start coords
        annotationStartY = physicalY;

        // Save state for undo functionality
        saveToHistory();

        if (['rect', 'circle', 'line', 'arrow', 'highlight', 'blur', 'triangle', 'diamond', 'star', 'polygon', 'connector', 'measurement'].includes(currentAnnotationTool)) {
            try {
                 tempCanvasData = screenshotCtx.getImageData(0, 0, screenshotCanvas.width, screenshotCanvas.height);
            } catch(getImageDataError) {
                 console.error("Error getting temp canvas data:", getImageDataError);
                 tempCanvasData = null;
                 alert("Cannot create shape preview.");
            }
        } else if (currentAnnotationTool === 'pen') {
             annotations.push({
                 type: 'pen',
                 color: annotationColor,
                 lineWidth: annotationLineWidth,
                 points: [{ x: annotationStartX, y: annotationStartY }]
             });
        } else if (currentAnnotationTool === 'brush') {
             annotations.push({
                 type: 'brush',
                 color: annotationColor,
                 lineWidth: brushSize,
                 opacity: brushOpacity,
                 brushSize: brushSize,
                 points: [{ x: annotationStartX, y: annotationStartY }]
             });
        }
    });

    screenshotCanvas.addEventListener('mousemove', (e) => {
        // Handle annotation dragging
        if (isDraggingAnnotation && selectedAnnotation) {
            const coords = getScrollAwareCanvasCoordinates(e);
            const currentX = coords.x;
            const currentY = coords.y;

            // Calculate movement delta
            const deltaX = currentX - annotationDragStartX;
            const deltaY = currentY - annotationDragStartY;

            // Move the annotation
            moveAnnotation(selectedAnnotation, deltaX, deltaY);

            // Update drag start position for next move
            annotationDragStartX = currentX;
            annotationDragStartY = currentY;

            // Handle auto-scroll
            handleAutoScroll(e);

            // Redraw canvas
            redrawScreenshotCanvas();
            return;
        }

        if (!isAnnotating || currentAnnotationTool === 'text') return;

        // Use scroll-aware coordinates for annotation drawing
        const coords = getScrollAwareCanvasCoordinates(e);
        const currentX = coords.x;
        const currentY = coords.y;

        screenshotCtx.strokeStyle = annotationColor;
        screenshotCtx.fillStyle = annotationColor;
        screenshotCtx.lineWidth = annotationLineWidth; // Use physical line width
        screenshotCtx.lineCap = 'round';
        screenshotCtx.lineJoin = 'round';

        if (['rect', 'circle', 'line', 'arrow', 'highlight', 'blur', 'triangle', 'diamond', 'star', 'polygon', 'connector', 'measurement'].includes(currentAnnotationTool)) {
            if (!tempCanvasData) return;
            redrawScreenshotCanvas(true); // Restore from snapshot

            // Apply current drawing styles
            screenshotCtx.strokeStyle = annotationColor;
            screenshotCtx.fillStyle = annotationColor;
            screenshotCtx.lineWidth = annotationLineWidth;
            applyDropShadow(screenshotCtx);
            setBorderStyle(screenshotCtx);

            screenshotCtx.beginPath();
             if (currentAnnotationTool === 'rect') {
                 screenshotCtx.strokeRect(annotationStartX, annotationStartY, currentX - annotationStartX, currentY - annotationStartY); // Draw using physical coords
             } else if (currentAnnotationTool === 'circle') {
                 const radiusP = Math.sqrt(Math.pow(currentX - annotationStartX, 2) + Math.pow(currentY - annotationStartY, 2)) / 2;
                 if (radiusP > 0.5) {
                    const centerXP = annotationStartX + (currentX - annotationStartX) / 2;
                    const centerYP = annotationStartY + (currentY - annotationStartY) / 2;
                    screenshotCtx.arc(centerXP, centerYP, radiusP, 0, 2 * Math.PI); // Draw using physical coords
                    screenshotCtx.stroke();
                 }
             } else if (currentAnnotationTool === 'line') {
                 screenshotCtx.moveTo(annotationStartX, annotationStartY); // Draw using physical coords
                 screenshotCtx.lineTo(currentX, currentY);
                 screenshotCtx.stroke();
             } else if (currentAnnotationTool === 'arrow') {
                 screenshotCtx.moveTo(annotationStartX, annotationStartY); // Draw using physical coords
                 screenshotCtx.lineTo(currentX, currentY);
                 const headlenP = 10 * (annotationLineWidth / 2);
                 const dxP = currentX - annotationStartX;
                 const dyP = currentY - annotationStartY;
                 if (Math.abs(dxP) > 0.1 || Math.abs(dyP) > 0.1) {
                     const angleP = Math.atan2(dyP, dxP);
                     screenshotCtx.moveTo(currentX, currentY);
                     screenshotCtx.lineTo(currentX - headlenP * Math.cos(angleP - Math.PI / 6), currentY - headlenP * Math.sin(angleP - Math.PI / 6));
                     screenshotCtx.moveTo(currentX, currentY);
                     screenshotCtx.lineTo(currentX - headlenP * Math.cos(angleP + Math.PI / 6), currentY - headlenP * Math.sin(angleP + Math.PI / 6));
                 }
                 screenshotCtx.stroke();
             } else if (currentAnnotationTool === 'highlight') {
                 // Draw a semi-transparent highlight rectangle
                 screenshotCtx.save();
                 screenshotCtx.globalAlpha = highlightOpacity;
                 screenshotCtx.fillStyle = annotationColor;
                 screenshotCtx.fillRect(annotationStartX, annotationStartY, currentX - annotationStartX, currentY - annotationStartY);
                 screenshotCtx.restore();
             } else if (currentAnnotationTool === 'blur') {
                 // Just draw a rectangle outline to show the area that will be blurred
                 screenshotCtx.setLineDash([5, 5]); // Dashed line
                 screenshotCtx.strokeStyle = 'rgba(255, 0, 0, 0.8)'; // Red dashed outline
                 screenshotCtx.strokeRect(annotationStartX, annotationStartY, currentX - annotationStartX, currentY - annotationStartY);
                 screenshotCtx.setLineDash([]); // Reset to solid line

                 // Add text to indicate blur area
                 screenshotCtx.fillStyle = 'rgba(255, 0, 0, 0.8)';
                 screenshotCtx.font = 'bold 14px sans-serif';
                 screenshotCtx.textAlign = 'center';
                 const centerX = annotationStartX + (currentX - annotationStartX) / 2;
                 const centerY = annotationStartY + (currentY - annotationStartY) / 2;
                 screenshotCtx.fillText('BLUR', centerX, centerY);

                 // Restore original stroke style
                 screenshotCtx.strokeStyle = annotationColor;
             } else if (currentAnnotationTool === 'triangle') {
                 // Preview triangle
                 const triWidth = currentX - annotationStartX;
                 const triCenterX = annotationStartX + triWidth / 2;
                 screenshotCtx.moveTo(triCenterX, annotationStartY);
                 screenshotCtx.lineTo(annotationStartX, currentY);
                 screenshotCtx.lineTo(currentX, currentY);
                 screenshotCtx.closePath();
                 screenshotCtx.stroke();
             } else if (currentAnnotationTool === 'diamond') {
                 // Preview diamond
                 const diamondCenterX = annotationStartX + (currentX - annotationStartX) / 2;
                 const diamondCenterY = annotationStartY + (currentY - annotationStartY) / 2;
                 screenshotCtx.moveTo(diamondCenterX, annotationStartY);
                 screenshotCtx.lineTo(currentX, diamondCenterY);
                 screenshotCtx.lineTo(diamondCenterX, currentY);
                 screenshotCtx.lineTo(annotationStartX, diamondCenterY);
                 screenshotCtx.closePath();
                 screenshotCtx.stroke();
             } else if (currentAnnotationTool === 'star') {
                 // Preview star
                 const starCenterX = annotationStartX + (currentX - annotationStartX) / 2;
                 const starCenterY = annotationStartY + (currentY - annotationStartY) / 2;
                 const starRadius = Math.min(Math.abs(currentX - annotationStartX), Math.abs(currentY - annotationStartY)) / 2;
                 const starPoints = 5;

                 for (let i = 0; i < starPoints * 2; i++) {
                     const angle = (i * Math.PI) / starPoints;
                     const radius = i % 2 === 0 ? starRadius : starRadius * 0.5;
                     const x = starCenterX + Math.cos(angle - Math.PI / 2) * radius;
                     const y = starCenterY + Math.sin(angle - Math.PI / 2) * radius;

                     if (i === 0) {
                         screenshotCtx.moveTo(x, y);
                     } else {
                         screenshotCtx.lineTo(x, y);
                     }
                 }
                 screenshotCtx.closePath();
                 screenshotCtx.stroke();
             } else if (currentAnnotationTool === 'polygon') {
                 // Preview polygon
                 const polyCenterX = annotationStartX + (currentX - annotationStartX) / 2;
                 const polyCenterY = annotationStartY + (currentY - annotationStartY) / 2;
                 const polyRadius = Math.min(Math.abs(currentX - annotationStartX), Math.abs(currentY - annotationStartY)) / 2;
                 const polyVertices = shapeVertices;

                 for (let i = 0; i < polyVertices; i++) {
                     const angle = (i * 2 * Math.PI) / polyVertices;
                     const x = polyCenterX + Math.cos(angle - Math.PI / 2) * polyRadius;
                     const y = polyCenterY + Math.sin(angle - Math.PI / 2) * polyRadius;

                     if (i === 0) {
                         screenshotCtx.moveTo(x, y);
                     } else {
                         screenshotCtx.lineTo(x, y);
                     }
                 }
                 screenshotCtx.closePath();
                 screenshotCtx.stroke();
             } else if (currentAnnotationTool === 'connector') {
                 // Preview connector with endpoints
                 screenshotCtx.moveTo(annotationStartX, annotationStartY);
                 screenshotCtx.lineTo(currentX, currentY);
                 screenshotCtx.stroke();

                 // Draw connection points
                 const connectorRadius = 4;
                 screenshotCtx.beginPath();
                 screenshotCtx.arc(annotationStartX, annotationStartY, connectorRadius, 0, 2 * Math.PI);
                 screenshotCtx.fill();
                 screenshotCtx.beginPath();
                 screenshotCtx.arc(currentX, currentY, connectorRadius, 0, 2 * Math.PI);
                 screenshotCtx.fill();
             } else if (currentAnnotationTool === 'measurement') {
                 // Preview measurement line
                 screenshotCtx.moveTo(annotationStartX, annotationStartY);
                 screenshotCtx.lineTo(currentX, currentY);
                 screenshotCtx.stroke();

                 // Show live measurement
                 const distance = Math.sqrt(Math.pow(currentX - annotationStartX, 2) + Math.pow(currentY - annotationStartY, 2));
                 let displayDistance;

                 switch (measurementUnit) {
                     case 'mm':
                         displayDistance = (distance * 0.264583).toFixed(1) + ' mm';
                         break;
                     case 'in':
                         displayDistance = (distance * 0.0104167).toFixed(2) + ' in';
                         break;
                     default:
                         displayDistance = Math.round(distance) + ' px';
                 }

                 const midX = (annotationStartX + currentX) / 2;
                 const midY = (annotationStartY + currentY) / 2;

                 screenshotCtx.save();
                 screenshotCtx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                 screenshotCtx.strokeStyle = annotationColor;
                 screenshotCtx.lineWidth = 1;

                 const textMetrics = screenshotCtx.measureText(displayDistance);
                 const textPadding = 4;
                 screenshotCtx.fillRect(
                     midX - textMetrics.width / 2 - textPadding,
                     midY - 10 - textPadding,
                     textMetrics.width + textPadding * 2,
                     20 + textPadding * 2
                 );

                 screenshotCtx.fillStyle = annotationColor;
                 screenshotCtx.textAlign = 'center';
                 screenshotCtx.textBaseline = 'middle';
                 screenshotCtx.fillText(displayDistance, midX, midY);
                 screenshotCtx.restore();
             }
        }
        else if (currentAnnotationTool === 'pen') {
             const currentStroke = annotations[annotations.length - 1];
             if (!currentStroke || currentStroke.type !== 'pen') return;
             currentStroke.points.push({ x: currentX, y: currentY }); // Store physical coords

             const points = currentStroke.points;
             if (points.length >= 2) {
                 screenshotCtx.beginPath();
                 const prevPoint = points[points.length - 2];
                 screenshotCtx.moveTo(prevPoint.x, prevPoint.y); // Draw using physical coords
                 screenshotCtx.lineTo(currentX, currentY);
                 screenshotCtx.stroke();
             }
        }
        else if (currentAnnotationTool === 'brush') {
             const currentStroke = annotations[annotations.length - 1];
             if (!currentStroke || currentStroke.type !== 'brush') return;
             currentStroke.points.push({ x: currentX, y: currentY }); // Store physical coords

             const points = currentStroke.points;
             if (points.length >= 2) {
                 screenshotCtx.save();
                 screenshotCtx.globalAlpha = currentStroke.opacity || brushOpacity;
                 screenshotCtx.lineCap = 'round';
                 screenshotCtx.lineJoin = 'round';
                 screenshotCtx.lineWidth = currentStroke.brushSize || brushSize;

                 screenshotCtx.beginPath();
                 const prevPoint = points[points.length - 2];
                 screenshotCtx.moveTo(prevPoint.x, prevPoint.y);
                 screenshotCtx.lineTo(currentX, currentY);
                 screenshotCtx.stroke();
                 screenshotCtx.restore();
             }
        }
    });

    screenshotCanvas.addEventListener('mouseup', (e) => {
        // Handle drag completion
        if (isDraggingAnnotation) {
            isDraggingAnnotation = false;
            stopAutoScroll();
            return;
        }

        if (!isAnnotating || currentAnnotationTool === 'text') return;
        isAnnotating = false;

        // Use scroll-aware coordinates
        const coords = getScrollAwareCanvasCoordinates(e);
        const finalX = coords.x;
        const finalY = coords.y;

        if (['rect', 'circle', 'line', 'arrow', 'highlight', 'blur', 'triangle', 'diamond', 'star', 'polygon', 'connector', 'measurement'].includes(currentAnnotationTool)) {
             if (Math.abs(finalX - annotationStartX) > 1 || Math.abs(finalY - annotationStartY) > 1) {
                 // Create the annotation object with common properties
                 const annotation = {
                    type: currentAnnotationTool,
                    color: annotationColor,
                    lineWidth: annotationLineWidth,
                    x1: annotationStartX,
                    y1: annotationStartY,
                    x2: finalX,
                    y2: finalY // Store physical coords
                 };

                 // Add tool-specific properties
                 if (currentAnnotationTool === 'blur') {
                     annotation.radius = blurRadius;
                 } else if (currentAnnotationTool === 'star') {
                     annotation.starPoints = 5; // Default star points
                     annotation.filled = false; // Default not filled
                 } else if (currentAnnotationTool === 'polygon') {
                     annotation.vertices = shapeVertices; // Number of vertices
                     annotation.filled = false; // Default not filled
                 } else if (['triangle', 'diamond'].includes(currentAnnotationTool)) {
                     annotation.filled = false; // Default not filled
                 }

                 // Apply visual enhancements
                 if (shadowEnabled) {
                     annotation.shadow = {
                         color: shadowColor,
                         blur: shadowBlur,
                         offsetX: shadowOffsetX,
                         offsetY: shadowOffsetY
                     };
                 }

                 if (borderStyle !== 'solid') {
                     annotation.borderStyle = borderStyle;
                 }

                 if (fillPattern === 'gradient') {
                     annotation.gradient = {
                         colors: [...gradientColors],
                         pattern: fillPattern
                     };
                 }

                 annotations.push(annotation);
             }
             tempCanvasData = null;
             redrawScreenshotCanvas();
        }
        else if (currentAnnotationTool === 'pen') {
             redrawScreenshotCanvas();
        }
    });

    screenshotCanvas.addEventListener('mouseleave', (e) => {
        if (isAnnotating && currentAnnotationTool !== 'text') {
             console.log("Mouse left canvas while annotating - finalizing.");
             isAnnotating = false;
             const dpr = window.devicePixelRatio || 1;
             const finalX = e.offsetX * dpr; // Use physical coords
             const finalY = e.offsetY * dpr;

             if (['rect', 'circle', 'line', 'arrow', 'highlight', 'blur'].includes(currentAnnotationTool)) {
                 if (Math.abs(finalX - annotationStartX) > 1 || Math.abs(finalY - annotationStartY) > 1) {
                    // Create the annotation object with common properties
                    const annotation = {
                        type: currentAnnotationTool,
                        color: annotationColor,
                        lineWidth: annotationLineWidth,
                        x1: annotationStartX,
                        y1: annotationStartY,
                        x2: finalX,
                        y2: finalY // Store physical coords
                    };

                    // Add tool-specific properties
                    if (currentAnnotationTool === 'blur') {
                        annotation.radius = blurRadius;
                    }

                    annotations.push(annotation);
                 }
                 tempCanvasData = null;
                 redrawScreenshotCanvas();
             } else if (currentAnnotationTool === 'pen') {
                 redrawScreenshotCanvas();
             }
        }
    });


    // --- Assemble Modal ---
    screenshotAnnotationModal.appendChild(modalTitle);
    screenshotAnnotationModal.appendChild(toolbar);
    screenshotAnnotationModal.appendChild(canvasContainer);
    document.body.appendChild(screenshotAnnotationModal);

    // Activate the default tool button after UI is built
    // Ensure arrowBtn exists before calling updateActiveAnnotationTool
    if(arrowBtn) {
        updateActiveAnnotationTool(arrowBtn);
    } else {
        console.warn("Default arrow button not found to set active state.");
    }



}

/** Updates the active state of annotation tool buttons and cursor */
function updateActiveAnnotationTool(activeButton) {
    const toolbar = document.getElementById(SCREENSHOT_TOOLBAR_ID);
    // Ensure toolbar, button, and canvas exist before proceeding
    if (toolbar && activeButton && screenshotCanvas) {
        // Remove 'active' from all tool buttons first
        toolbar.querySelectorAll('button[data-tool]').forEach(btn => btn.classList.remove('active'));
        // Add 'active' to the clicked button
        activeButton.classList.add('active');

        // Get the tool type from the button's data attribute
        const toolType = activeButton.dataset.tool;
        if (toolType) {
            // Make sure currentAnnotationTool is set to match the button
            currentAnnotationTool = toolType;
        }

        // Update canvas cursor based on the selected tool
        if (currentAnnotationTool === 'text') {
            screenshotCanvas.style.cursor = 'text';
        } else if (currentAnnotationTool === 'pen') {
            screenshotCanvas.style.cursor = 'crosshair';
        } else if (currentAnnotationTool === 'eraser') {
            screenshotCanvas.style.cursor = 'cell';
        } else if (currentAnnotationTool === 'highlight') {
            screenshotCanvas.style.cursor = 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\'%3E%3Cpath fill=\'%23ffff00\' d=\'M3 17h18v2H3v-2zm16-10v2H5v-2h14m2-2H3v6h18V5z\'/%3E%3C/svg%3E") 0 24, auto';
        } else if (currentAnnotationTool === 'blur') {
            screenshotCanvas.style.cursor = 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\'%3E%3Cpath fill=\'%23ff0000\' d=\'M3 4v16h18V4H3zm16 14H5V6h14v12z\'/%3E%3C/svg%3E") 0 24, auto';
        } else if (currentAnnotationTool === 'callout') {
            screenshotCanvas.style.cursor = 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\'%3E%3Cpath fill=\'%234285f4\' d=\'M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z\'/%3E%3C/svg%3E") 0 24, auto';
        } else {
            screenshotCanvas.style.cursor = 'crosshair';
        }
    } else {
         console.warn("updateActiveAnnotationTool: Cannot update - toolbar, active button, or canvas missing.");
    }
}

/** Opens the screenshot annotation modal and loads the image */
function openScreenshotAnnotationModal(imageDataUrl) {
    if (!imageDataUrl) {
        console.error("Screenshot Error: No image data URL provided.");
        showNotification("Failed to capture screenshot. No image data received.", "error");
        return;
    }
    if (!screenshotAnnotationModal) {
        createScreenshotAnnotationModal(); // Ensure modal exists
        // Check again if creation failed
        if (!screenshotAnnotationModal) {
             console.error("Screenshot Error: Failed to create the annotation modal.");
             showNotification("Failed to open screenshot editor.", "error");
             return;
        }
    }

    // Reset state
    annotations = [];
    isAnnotating = false;
    currentAnnotationTool = 'arrow';
    annotationColor = '#FF0000';
    annotationLineWidth = 2; // Default physical line width
    tempCanvasData = null;

    // Reset drag-and-drop state
    selectedAnnotation = null;
    isDraggingAnnotation = false;
    annotationDragStartX = 0;
    annotationDragStartY = 0;
    annotationDragOffsetX = 0;
    annotationDragOffsetY = 0;
    stopAutoScroll();

    // Reset modal drag state
    isModalDragging = false;
    modalDragStartX = 0;
    modalDragStartY = 0;
    modalInitialLeft = 0;
    modalInitialTop = 0;

    // Find canvas and context
    screenshotCanvas = document.getElementById(SCREENSHOT_CANVAS_ID);
    if (!screenshotCanvas) {
        console.error("Screenshot Error: Canvas element not found:", SCREENSHOT_CANVAS_ID);
        showNotification("Screenshot editor canvas missing.", "error");
        return;
    }
    // Get canvas context with optimal settings for high-quality rendering
    screenshotCtx = screenshotCanvas.getContext('2d', {
        willReadFrequently: true,
        alpha: false, // Disable alpha channel for better performance and quality
        desynchronized: false, // Ensure synchronous rendering for quality
        colorSpace: 'srgb' // Use standard RGB color space
    });
     if (!screenshotCtx) {
        console.error("Screenshot Error: Failed to get 2D context for canvas.");
        showNotification("Screenshot editor context failed.", "error");
        return;
    }

    // --- Load image and setup canvas dimensions --- PURELY PHYSICAL PIXELS ---
    screenshotImg = new Image();
    screenshotImg.onload = () => {
        const dpr = window.devicePixelRatio || 1;
        const imgWidth = screenshotImg.naturalWidth; // Assume these are physical pixels
        const imgHeight = screenshotImg.naturalHeight;

        // Smart scaling algorithm for full-page vs viewport screenshots
        const scalingResult = calculateOptimalScreenshotScale(imgWidth, imgHeight, dpr);
        const finalLogicalWidth = scalingResult.logicalWidth;
        const finalLogicalHeight = scalingResult.logicalHeight;



        // --- Set Canvas Physical and Logical Size ---
        const physicalWidth = Math.round(finalLogicalWidth * dpr);
        const physicalHeight = Math.round(finalLogicalHeight * dpr);
        screenshotCanvas.width = physicalWidth;
        screenshotCanvas.height = physicalHeight;
        screenshotCanvas.style.width = `${finalLogicalWidth}px`;
        screenshotCanvas.style.height = `${finalLogicalHeight}px`;

        // --- Enhanced Image Quality Settings for Maximum Clarity ---
        screenshotCtx.imageSmoothingEnabled = true;
        screenshotCtx.imageSmoothingQuality = "high";

        // Set optimal rendering settings
        screenshotCtx.globalCompositeOperation = 'source-over';
        screenshotCtx.filter = 'none'; // Disable any filters that might degrade quality
        screenshotCtx.textRenderingOptimization = 'optimizeQuality';

        // Ensure pixel-perfect rendering for crisp images
        screenshotCtx.translate(0.5, 0.5); // Align to pixel boundaries
        screenshotCtx.translate(-0.5, -0.5); // Reset translation

        // Additional quality improvements for all images
        screenshotCtx.lineWidth = 1;
        screenshotCtx.lineCap = 'butt';
        screenshotCtx.lineJoin = 'miter';

        // Set modal size based on screenshot type and scaling result
        const modalSizing = calculateModalSize(scalingResult, finalLogicalWidth, finalLogicalHeight);

        screenshotAnnotationModal.style.width = `${modalSizing.width}px`;
        screenshotAnnotationModal.style.height = `${modalSizing.height}px`;
        screenshotAnnotationModal.style.minWidth = '400px';
        screenshotAnnotationModal.style.minHeight = '300px';

        // Update canvas container max dimensions for scrolling
        if (canvasContainer) {
            canvasContainer.style.maxWidth = `${modalSizing.canvasMaxWidth}px`;
            canvasContainer.style.maxHeight = `${modalSizing.canvasMaxHeight}px`;
        }



        // Perform initial draw (will use physical dimensions)
        redrawScreenshotCanvas();

        // Make modal visible
        screenshotAnnotationModal.style.display = 'flex';
        // REMOVED centerElement call - rely on CSS

        // Update toolbar state
        updateActiveAnnotationTool(document.querySelector(`#${SCREENSHOT_TOOLBAR_ID} button[data-tool="${currentAnnotationTool}"]`));

    };
    screenshotImg.onerror = (error) => {
        console.error("Screenshot Error: Failed to load image data URL:", error);
        showNotification("Failed to load screenshot image.", "error");
        closeScreenshotAnnotationModal();
    };
    screenshotImg.src = imageDataUrl;
}

// Make the function globally available
window.openScreenshotAnnotationModal = openScreenshotAnnotationModal;

/** Closes the screenshot annotation modal and cleans up */
function closeScreenshotAnnotationModal() {
    if (screenshotAnnotationModal) {
        screenshotAnnotationModal.style.display = 'none';
        screenshotAnnotationModal.setAttribute('aria-hidden', 'true');
    }
    // Clean up any temporary text input overlays
    screenshotCanvas?.parentElement?.querySelectorAll('input.Stickara-temp-text-input').forEach(el => el.remove());

    // Reset annotation state variables to defaults
    isAnnotating = false;
    annotations = [];
    tempCanvasData = null;
    annotationColor = '#FF0000';
    annotationLineWidth = 2;
    currentAnnotationTool = 'arrow';

    // Reset drag-and-drop state
    selectedAnnotation = null;
    isDraggingAnnotation = false;
    annotationDragStartX = 0;
    annotationDragStartY = 0;
    annotationDragOffsetX = 0;
    annotationDragOffsetY = 0;
    stopAutoScroll();

    // Reset modal drag state
    stopModalDrag(); // Clean up any active drag
    isModalDragging = false;
    modalDragStartX = 0;
    modalDragStartY = 0;
    modalInitialLeft = 0;
    modalInitialTop = 0;

    // Optional: Return focus to the element that opened the modal or a default element
    // e.g., document.getElementById(NOTE_ID)?.focus();
}

/** Handles the 'Save & Insert' button click: shows preview and then sends final image data to background */
async function handleSaveScreenshot() {
    // Ensure canvas exists before proceeding
    if (!screenshotCanvas || !screenshotCtx) {
        console.error("Screenshot Error: Canvas element not found:", SCREENSHOT_CANVAS_ID);

        // Try to find the canvas element
        screenshotCanvas = document.getElementById(SCREENSHOT_CANVAS_ID);
        if (screenshotCanvas) {
            screenshotCtx = screenshotCanvas.getContext('2d');
            console.log("Stickara: Found existing canvas element, proceeding with save");
        } else {
            alert("Error: Screenshot canvas not available for saving. Please try taking the screenshot again.");
            return;
        }
    }
    // Ensure final drawing state is on the canvas
    redrawScreenshotCanvas();

    const toolbar = document.getElementById(SCREENSHOT_TOOLBAR_ID);
    // Disable all buttons during save operation
    toolbar?.querySelectorAll('button').forEach(b => b.disabled = true);

    try {
        // Generate filename
        const filenamePrefix = generateScreenshotFilenamePrefix();
        let filename = `${filenamePrefix}.png`;

        // Get quality preferences
        const qualityPrefs = await getScreenshotQualityPreferences();

        // Process the image using a web worker if available
        let finalImageDataUrl;

        if (window.StickaraWorkerManager) {

            try {
                // Configure canvas for high-DPI if enabled
                if (qualityPrefs.enableHighDpi) {
                    // Make sure we're using the device pixel ratio for the canvas
                    const dpr = window.devicePixelRatio || 1;
                    if (dpr > 1) {
                        console.log(`SB Screenshot: Using high-DPI rendering with DPR ${dpr}`);
                    }
                }

                // Configure anti-aliasing
                if (screenshotCtx && typeof screenshotCtx.imageSmoothingEnabled !== 'undefined') {
                    screenshotCtx.imageSmoothingEnabled = qualityPrefs.enableAntiAliasing;
                    if (qualityPrefs.enableAntiAliasing && typeof screenshotCtx.imageSmoothingQuality !== 'undefined') {
                        screenshotCtx.imageSmoothingQuality = 'high';
                    }
                }

                // Redraw with new settings if needed
                redrawScreenshotCanvas();

                // Get the raw image data with maximum quality
                const rawImageDataUrl = screenshotCanvas.toDataURL('image/png', 1.0);

                // Determine quality settings based on user preference
                let imageQuality = 0.92; // Default (standard)
                if (qualityPrefs.quality === 'high') {
                    imageQuality = 0.96;
                } else if (qualityPrefs.quality === 'maximum') {
                    imageQuality = 1.0;
                }



                // Process the image in a web worker
                const result = await window.StickaraWorkerManager.runTask(
                    'workers/image-worker.js',
                    'processImage',
                    {
                        imageDataUrl: rawImageDataUrl,
                        options: {
                            format: 'image/png',
                            quality: imageQuality,
                            enableAntiAliasing: qualityPrefs.enableAntiAliasing
                        }
                    }
                );

                // Extract the processed data URL from various possible result structures
                finalImageDataUrl = extractDataUrlFromWorkerResult(result, 'processedDataUrl', rawImageDataUrl);
            } catch (workerError) {
                console.error("SB Screenshot: Error processing image in worker:", workerError);
                // Fall back to direct canvas conversion with maximum quality
                finalImageDataUrl = screenshotCanvas.toDataURL('image/png', 1.0);
            }
        } else {
            // No worker available, use direct canvas conversion with maximum quality
            finalImageDataUrl = screenshotCanvas.toDataURL('image/png', 1.0);
        }

        // Show preview dialog and get user confirmation and possibly updated filename
        const previewResult = await showScreenshotPreviewDialog(finalImageDataUrl, filename);

        // If user cancelled, re-enable buttons and return
        if (!previewResult.confirmed) {
            console.log("SB Screenshot: User cancelled from preview");
            toolbar?.querySelectorAll('button').forEach(b => b.disabled = false);
            return;
        }

        // Update filename if user changed it
        if (previewResult.filename && previewResult.filename !== filename) {
            console.log(`SB Screenshot: User renamed file from ${filename} to ${previewResult.filename}`);
            filename = previewResult.filename;

            // Ensure filename ends with .png
            if (!filename.toLowerCase().endsWith('.png')) {
                filename += '.png';
            }
        }

        // Get screenshot storage preferences with error handling
        let storagePrefs;
        try {
            storagePrefs = await getScreenshotStoragePreferences();
        } catch (error) {
            console.error("Stickara: Error getting storage preferences:", error);
            storagePrefs = {
                storageLocation: 'local',
                driveFolderName: 'Stickara Screenshots',
                useAppDataFolder: true,
                autoDeleteAfter: 0,
                askBeforeUpload: true
            };
        }

        // Ensure storagePrefs is valid with fallback
        if (!storagePrefs || typeof storagePrefs !== 'object') {
            console.warn("Stickara: Invalid storage preferences, using defaults");
            storagePrefs = {
                storageLocation: 'local',
                driveFolderName: 'Stickara Screenshots',
                useAppDataFolder: true,
                autoDeleteAfter: 0,
                askBeforeUpload: true
            };
        }

        // Show storage notification
        showStorageNotification(storagePrefs);

        // Handle storage based on preferences with safe property access
        const storageLocation = storagePrefs.storageLocation || 'local';

        if (storageLocation === 'local' || storageLocation === 'both') {
            // Save locally
            await saveScreenshotLocally(finalImageDataUrl, filename);
            insertLocalScreenshotPlaceholder(filenamePrefix);
        }

        if (storageLocation === 'drive' || storageLocation === 'both') {
            // Check if Drive is enabled
            const driveStatusResponse = await chrome.runtime.sendMessage({ action: 'getDriveSyncStatus' });

            if (driveStatusResponse?.driveEnabled) {
                console.log("SB Screenshot: Drive enabled, attempting upload...");

                // If askBeforeUpload is enabled, show custom confirmation dialog
                if (storagePrefs.askBeforeUpload) {
                    // Create and show the custom confirmation dialog
                    const confirmed = await showDriveUploadConfirmationDialog(storagePrefs, filename);
                    if (!confirmed) {
                        console.log("SB Screenshot: User declined Drive upload");
                        toolbar?.querySelectorAll('button').forEach(b => b.disabled = false);
                        closeScreenshotAnnotationModal();
                        return;
                    }
                }

                // Send data URL and filename to background for upload
                chrome.runtime.sendMessage({
                    action: 'uploadScreenshotToDrive',
                    imageDataUrl: finalImageDataUrl,
                    filename: filename,
                    folderName: storagePrefs.driveFolderName,
                    useAppDataFolder: storagePrefs.useAppDataFolder
                }, (uploadResponse) => {
                    // Re-enable buttons ONLY after response received
                    toolbar?.querySelectorAll('button').forEach(b => b.disabled = false);

                    if (uploadResponse?.success && uploadResponse.fileId) {
                        console.log("SB Screenshot: Drive upload successful. File ID:", uploadResponse.fileId);
                        // Insert a non-clickable link/placeholder indicating Drive save
                        // Use specific classes for styling
                        const driveLinkHTML = `<p><span class="Stickara-drive-link screenshot-link" data-drive-id="${uploadResponse.fileId}" title="Screenshot saved to Drive: ${filename}">🖼️ Screenshot (${filenamePrefix}) [Drive]</span></p>`;

                        // Only insert if we haven't already inserted a local placeholder
                        if (storagePrefs.storageLocation !== 'both') {
                            insertHtmlAtCursor(driveLinkHTML + '<br>');
                        }

                        closeScreenshotAnnotationModal();

                        // Schedule auto-deletion if enabled
                        if (storagePrefs.autoDeleteAfter > 0) {
                            scheduleScreenshotDeletion(uploadResponse.fileId, storagePrefs.autoDeleteAfter);
                        }
                    } else {
                        console.error("SB Screenshot: Drive upload failed.", uploadResponse?.error);
                        alert(`Failed to save screenshot to Google Drive: ${uploadResponse?.error || 'Unknown error'}\n\nInserting local placeholder instead.`);

                        // Only insert if we haven't already inserted a local placeholder
                        if (storagePrefs.storageLocation === 'drive') {
                            insertLocalScreenshotPlaceholder(filenamePrefix);
                        }

                        closeScreenshotAnnotationModal();
                    }
                });
            } else { // Drive not enabled
                console.log("SB Screenshot: Drive not enabled. Inserting local placeholder.");

                // Only insert if we haven't already inserted a local placeholder
                if (storagePrefs.storageLocation === 'drive') {
                    insertLocalScreenshotPlaceholder(filenamePrefix);
                }

                toolbar?.querySelectorAll('button').forEach(b => b.disabled = false);
                closeScreenshotAnnotationModal();
            }
        } else {
            // If not saving to Drive, re-enable buttons and close modal
            toolbar?.querySelectorAll('button').forEach(b => b.disabled = false);
            closeScreenshotAnnotationModal();
        }
    } catch (error) {
        console.error("SB Screenshot: Error saving screenshot:", error);
        alert(`Error saving screenshot: ${error.message || 'Unknown error'}\n\nInserting local placeholder.`);
        insertLocalScreenshotPlaceholder(generateScreenshotFilenamePrefix());
        toolbar?.querySelectorAll('button').forEach(b => b.disabled = false);
        closeScreenshotAnnotationModal();
    }
}

/** Handles the 'Insert into Note' button click: embeds the annotated screenshot directly into the note */
async function handleInsertScreenshotIntoNote() {
    if (!screenshotCanvas || !screenshotCtx) {
        alert("Error: Screenshot canvas not available for insertion.");
        return;
    }

    // Check if note text area is available
    if (typeof insertHtmlAtCursor !== 'function') {
        console.error("Stickara Error: insertHtmlAtCursor function not found. Cannot insert screenshot into note.");
        alert("Could not insert screenshot into note. Note editor not available.");
        return;
    }

    // Ensure final drawing state is on the canvas
    redrawScreenshotCanvas();

    const toolbar = document.getElementById(SCREENSHOT_TOOLBAR_ID);
    // Disable all buttons during insert operation
    toolbar?.querySelectorAll('button').forEach(b => b.disabled = true);

    try {
        // Get quality preferences for image compression
        const qualityPrefs = await getScreenshotQualityPreferences();

        // Configure canvas for high-quality rendering if enabled
        if (qualityPrefs.enableHighDpi) {
            const dpr = window.devicePixelRatio || 1;
            if (dpr > 1) {
                console.log(`SB Screenshot: Using high-DPI rendering with DPR ${dpr} for note insertion`);
            }
        }

        // Configure anti-aliasing
        if (screenshotCtx && typeof screenshotCtx.imageSmoothingEnabled !== 'undefined') {
            screenshotCtx.imageSmoothingEnabled = qualityPrefs.enableAntiAliasing;
            if (qualityPrefs.enableAntiAliasing && typeof screenshotCtx.imageSmoothingQuality !== 'undefined') {
                screenshotCtx.imageSmoothingQuality = 'high';
            }
        }

        // Redraw with quality settings
        redrawScreenshotCanvas();

        // Convert canvas to base64 data URL with appropriate quality
        let imageQuality = 0.85; // Default quality for note embedding (smaller file size)
        if (qualityPrefs.quality === 'high') {
            imageQuality = 0.92;
        } else if (qualityPrefs.quality === 'maximum') {
            imageQuality = 0.95; // Still compress slightly for note storage
        }

        const imageDataUrl = screenshotCanvas.toDataURL('image/png', imageQuality);

        // Check image size and warn user if it's very large
        const imageSizeKB = Math.round((imageDataUrl.length * 0.75) / 1024); // Approximate size in KB
        const maxRecommendedSizeKB = 500; // 500KB recommended limit

        if (imageSizeKB > maxRecommendedSizeKB) {
            const proceed = confirm(
                `This screenshot is quite large (${imageSizeKB}KB). ` +
                `Large embedded images may slow down note loading and syncing.\n\n` +
                `Recommended: Use "Save Screenshot" instead for large images.\n\n` +
                `Continue with embedding?`
            );
            if (!proceed) {
                toolbar?.querySelectorAll('button').forEach(b => b.disabled = false);
                return;
            }
        }

        // Create image element with the screenshot
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const altText = `Screenshot ${timestamp}`;

        // Create HTML for the embedded image with proper styling
        const imageHtml = `<p><img src="${imageDataUrl}" alt="${altText}" style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 4px; margin: 10px 0; display: block;" title="Embedded Screenshot - ${timestamp}"></p>`;

        // Insert the image into the note at cursor position
        insertHtmlAtCursor(imageHtml);

        // Show success feedback
        console.log(`SB Screenshot: Successfully embedded screenshot into note (${imageSizeKB}KB)`);

        // Close the annotation modal
        closeScreenshotAnnotationModal();

        // Show user feedback
        if (typeof showStatus === 'function') {
            showStatus(`Screenshot embedded into note (${imageSizeKB}KB)`, 'success');
        }

    } catch (error) {
        console.error("SB Screenshot: Error inserting screenshot into note:", error);
        alert(`Error inserting screenshot into note: ${error.message || 'Unknown error'}`);
    } finally {
        // Re-enable buttons
        toolbar?.querySelectorAll('button').forEach(b => b.disabled = false);
    }
}

/** Handles the Direct Download Button click */
async function handleDownloadAnnotatedScreenshot() {
    if (!screenshotCanvas || !screenshotCtx) {
        alert("Error: Cannot download, canvas not available.");
        return;
    }


    // Ensure the latest annotations are drawn onto the canvas
    redrawScreenshotCanvas();

    try {
        let imageDataUrl;
        const filename = generateScreenshotFilenamePrefix() + '.png'; // Generate filename

        // Get quality preferences
        const qualityPrefs = await getScreenshotQualityPreferences();

        // Process the image using a web worker if available
        if (window.StickaraWorkerManager) {

            try {
                // Configure canvas for high-DPI if enabled
                if (qualityPrefs.enableHighDpi) {
                    // Make sure we're using the device pixel ratio for the canvas
                    const dpr = window.devicePixelRatio || 1;
                    if (dpr > 1) {
                        console.log(`SB Screenshot: Using high-DPI rendering with DPR ${dpr} for download`);
                    }
                }

                // Configure anti-aliasing
                if (screenshotCtx && typeof screenshotCtx.imageSmoothingEnabled !== 'undefined') {
                    screenshotCtx.imageSmoothingEnabled = qualityPrefs.enableAntiAliasing;
                    if (qualityPrefs.enableAntiAliasing && typeof screenshotCtx.imageSmoothingQuality !== 'undefined') {
                        screenshotCtx.imageSmoothingQuality = 'high';
                    }
                }

                // Redraw with new settings if needed
                redrawScreenshotCanvas();

                // Get the raw image data with maximum quality
                const rawImageDataUrl = screenshotCanvas.toDataURL('image/png', 1.0);

                // Determine quality settings based on user preference
                let imageQuality = 0.95; // Default for download (slightly higher than save)
                if (qualityPrefs.quality === 'high') {
                    imageQuality = 0.98;
                } else if (qualityPrefs.quality === 'maximum') {
                    imageQuality = 1.0;
                }



                // Validate image data before sending to worker
                if (!rawImageDataUrl || !rawImageDataUrl.startsWith('data:image/')) {
                    console.error("SB Screenshot: Invalid image data URL for worker processing:", rawImageDataUrl?.substring(0, 50));
                    imageDataUrl = screenshotCanvas.toDataURL('image/png');
                } else {
                    // Check if image is too large for worker processing
                    const maxWorkerImageSize = 50 * 1024 * 1024; // 50MB limit
                    if (rawImageDataUrl.length > maxWorkerImageSize) {
                        imageDataUrl = screenshotCanvas.toDataURL('image/png');
                    } else {

                    // Process the image in a web worker
                    const result = await window.StickaraWorkerManager.runTask(
                        'workers/image-worker.js',
                        'convertFormat',
                        {
                            imageDataUrl: rawImageDataUrl,
                            format: 'image/png',
                            options: {
                                quality: imageQuality,
                                enableAntiAliasing: qualityPrefs.enableAntiAliasing
                            }
                        }
                    );

                    // Extract the converted data URL from various possible result structures
                    imageDataUrl = extractDataUrlFromWorkerResult(result, 'convertedDataUrl', rawImageDataUrl);
                    }
                }
            } catch (workerError) {
                console.error("SB Screenshot: Error processing download image in worker:", workerError);
                // Fall back to direct canvas conversion with maximum quality
                imageDataUrl = screenshotCanvas.toDataURL('image/png', 1.0);
            }
        } else {
            // No worker available, use direct canvas conversion with maximum quality
            imageDataUrl = screenshotCanvas.toDataURL('image/png', 1.0);
        }

        // Create a temporary link element to trigger the download
        const link = document.createElement('a');
        link.href = imageDataUrl;
        link.download = filename; // Set the desired filename

        // Append to body, programmatically click, and then remove the link
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);


        // Optionally provide user feedback (though browser usually indicates download)
        // showStatus('Download started!', 'success'); // Requires showStatus function

        // Close the modal after initiating the download
        closeScreenshotAnnotationModal();

    } catch (error) {
        console.error("SB Screenshot: Error generating data URL or triggering download:", error);
        // Check for specific errors like security restrictions
        if (error instanceof DOMException && error.name === 'SecurityError') {
             alert("Failed to prepare screenshot for download due to canvas security restrictions (e.g., tainted canvas).");
        } else {
             alert("Failed to prepare screenshot for download. See console for details.");
        }
    }
}


/**
 * Gets the screenshot storage preferences with robust error handling
 * @returns {Promise<Object>} The storage preferences
 */
async function getScreenshotStoragePreferences() {
    return new Promise((resolve, reject) => {
        try {
            const defaultPrefs = {
                storageLocation: 'local', // 'local', 'drive', or 'both'
                driveFolderName: 'Stickara Screenshots',
                useAppDataFolder: true, // Use application-specific folder (not visible in Drive UI)
                autoDeleteAfter: 0, // 0 = never, otherwise days
                askBeforeUpload: true // Ask before uploading to Drive
            };

            // Check if chrome.storage is available
            if (!chrome || !chrome.storage || !chrome.storage.local) {
                console.warn("Stickara: Chrome storage not available, using default preferences");
                resolve(defaultPrefs);
                return;
            }

            chrome.storage.local.get(['screenshotStoragePrefs'], result => {
                const error = chrome.runtime.lastError;
                if (error) {
                    console.error("Stickara: Error accessing storage for preferences:", error.message);
                    resolve(defaultPrefs);
                    return;
                }

                try {
                    // Safely access and validate the stored preferences
                    let prefs = defaultPrefs;

                    if (result && typeof result === 'object' && result.screenshotStoragePrefs) {
                        const storedPrefs = result.screenshotStoragePrefs;

                        // Validate and merge with defaults
                        if (typeof storedPrefs === 'object' && storedPrefs !== null) {
                            prefs = {
                                ...defaultPrefs,
                                ...storedPrefs
                            };

                            // Validate specific properties
                            if (!['local', 'drive', 'both'].includes(prefs.storageLocation)) {
                                console.warn("Stickara: Invalid storageLocation, using default");
                                prefs.storageLocation = 'local';
                            }

                            if (typeof prefs.driveFolderName !== 'string' || !prefs.driveFolderName.trim()) {
                                prefs.driveFolderName = 'Stickara Screenshots';
                            }

                            if (typeof prefs.useAppDataFolder !== 'boolean') {
                                prefs.useAppDataFolder = true;
                            }

                            if (typeof prefs.autoDeleteAfter !== 'number' || prefs.autoDeleteAfter < 0) {
                                prefs.autoDeleteAfter = 0;
                            }

                            if (typeof prefs.askBeforeUpload !== 'boolean') {
                                prefs.askBeforeUpload = true;
                            }
                        }
                    }

                    console.log("Stickara: Loaded storage preferences:", prefs);
                    resolve(prefs);

                } catch (parseError) {
                    console.error("Stickara: Error parsing storage preferences:", parseError);
                    resolve(defaultPrefs);
                }
            });

        } catch (error) {
            console.error("Stickara: Exception in getScreenshotStoragePreferences:", error);
            reject(error);
        }
    });
}

/**
 * Gets the screenshot quality preferences
 * @returns {Promise<Object>} The quality preferences
 */
async function getScreenshotQualityPreferences() {
    return new Promise(resolve => {
        chrome.storage.local.get(['screenshotQualityPrefs'], result => {
            const defaultPrefs = {
                quality: 'standard', // 'standard', 'high', or 'maximum'
                enableHighDpi: true, // Enable high-DPI support for retina displays
                enableAntiAliasing: true // Enable anti-aliasing for smoother lines
            };

            const prefs = result.screenshotQualityPrefs || defaultPrefs;
            resolve(prefs);
        });
    });
}

/**
 * Shows a preview dialog for the screenshot with options to rename and confirm
 * @param {string} imageDataUrl - The image data URL to preview
 * @param {string} filename - The suggested filename
 * @returns {Promise<Object>} - Object with confirmed status and possibly updated filename
 */
async function showScreenshotPreviewDialog(imageDataUrl, filename) {
    return new Promise(resolve => {
        // Create the preview dialog container
        const dialogOverlay = document.createElement('div');
        dialogOverlay.className = 'stickara-dialog-overlay';
        dialogOverlay.style.position = 'fixed';
        dialogOverlay.style.top = '0';
        dialogOverlay.style.left = '0';
        dialogOverlay.style.width = '100%';
        dialogOverlay.style.height = '100%';
        dialogOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        dialogOverlay.style.display = 'flex';
        dialogOverlay.style.justifyContent = 'center';
        dialogOverlay.style.alignItems = 'center';
        dialogOverlay.style.zIndex = '10001';

        // Create the dialog box
        const dialog = document.createElement('div');
        dialog.className = 'stickara-preview-dialog';
        dialog.style.backgroundColor = 'white';
        dialog.style.borderRadius = '8px';
        dialog.style.boxShadow = '0 4px 24px rgba(0, 0, 0, 0.3)';
        dialog.style.width = '80%';
        dialog.style.maxWidth = '800px';
        dialog.style.maxHeight = '90vh';
        dialog.style.display = 'flex';
        dialog.style.flexDirection = 'column';
        dialog.style.animation = 'stickara-dialog-fade-in 0.3s ease-out';

        // Add animation styles if not already added
        if (!document.getElementById('stickara-dialog-animations')) {
            const style = document.createElement('style');
            style.id = 'stickara-dialog-animations';
            style.textContent = `
                @keyframes stickara-dialog-fade-in {
                    from { opacity: 0; transform: scale(0.95); }
                    to { opacity: 1; transform: scale(1); }
                }
            `;
            document.head.appendChild(style);
        }

        // Create dialog header
        const header = document.createElement('div');
        header.style.padding = '15px 20px';
        header.style.borderBottom = '1px solid #eee';
        header.style.display = 'flex';
        header.style.justifyContent = 'space-between';
        header.style.alignItems = 'center';

        const title = document.createElement('h3');
        title.textContent = 'Screenshot Preview';
        title.style.margin = '0';
        title.style.fontSize = '18px';
        title.style.fontWeight = 'bold';
        title.style.color = '#333';

        const closeButton = document.createElement('button');
        closeButton.innerHTML = '&times;';
        closeButton.style.background = 'none';
        closeButton.style.border = 'none';
        closeButton.style.fontSize = '24px';
        closeButton.style.cursor = 'pointer';
        closeButton.style.color = '#666';
        closeButton.style.padding = '0 5px';
        closeButton.title = 'Close preview';

        header.appendChild(title);
        header.appendChild(closeButton);

        // Create preview content
        const content = document.createElement('div');
        content.style.padding = '20px';
        content.style.overflow = 'auto';
        content.style.flex = '1';
        content.style.display = 'flex';
        content.style.flexDirection = 'column';
        content.style.alignItems = 'center';

        // Create image preview
        const imagePreview = document.createElement('img');
        imagePreview.src = imageDataUrl;
        imagePreview.style.maxWidth = '100%';
        imagePreview.style.maxHeight = '60vh';
        imagePreview.style.objectFit = 'contain';
        imagePreview.style.marginBottom = '20px';
        imagePreview.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
        imagePreview.style.borderRadius = '4px';

        // Create filename input
        const filenameContainer = document.createElement('div');
        filenameContainer.style.width = '100%';
        filenameContainer.style.marginBottom = '20px';

        const filenameLabel = document.createElement('label');
        filenameLabel.textContent = 'Filename:';
        filenameLabel.style.display = 'block';
        filenameLabel.style.marginBottom = '5px';
        filenameLabel.style.fontWeight = 'bold';
        filenameLabel.style.color = '#555';

        const filenameInput = document.createElement('input');
        filenameInput.type = 'text';
        filenameInput.value = filename.replace(/\.png$/i, ''); // Remove extension for cleaner UI
        filenameInput.style.width = '100%';
        filenameInput.style.padding = '8px 12px';
        filenameInput.style.border = '1px solid #ddd';
        filenameInput.style.borderRadius = '4px';
        filenameInput.style.fontSize = '14px';

        const extensionSpan = document.createElement('span');
        extensionSpan.textContent = '.png';
        extensionSpan.style.color = '#888';
        extensionSpan.style.marginLeft = '5px';

        const inputWrapper = document.createElement('div');
        inputWrapper.style.display = 'flex';
        inputWrapper.style.alignItems = 'center';

        inputWrapper.appendChild(filenameInput);
        inputWrapper.appendChild(extensionSpan);

        filenameContainer.appendChild(filenameLabel);
        filenameContainer.appendChild(inputWrapper);

        // Create footer with buttons
        const footer = document.createElement('div');
        footer.style.padding = '15px 20px';
        footer.style.borderTop = '1px solid #eee';
        footer.style.display = 'flex';
        footer.style.justifyContent = 'flex-end';
        footer.style.gap = '10px';

        const cancelButton = document.createElement('button');
        cancelButton.textContent = 'Cancel';
        cancelButton.style.padding = '8px 16px';
        cancelButton.style.border = '1px solid #ddd';
        cancelButton.style.borderRadius = '4px';
        cancelButton.style.backgroundColor = '#f5f5f5';
        cancelButton.style.color = '#333';
        cancelButton.style.cursor = 'pointer';
        cancelButton.style.fontSize = '14px';

        const saveButton = document.createElement('button');
        saveButton.textContent = 'Save Screenshot';
        saveButton.style.padding = '8px 16px';
        saveButton.style.border = 'none';
        saveButton.style.borderRadius = '4px';
        saveButton.style.backgroundColor = '#4285f4';
        saveButton.style.color = 'white';
        saveButton.style.cursor = 'pointer';
        saveButton.style.fontSize = '14px';
        saveButton.style.fontWeight = 'bold';

        footer.appendChild(cancelButton);
        footer.appendChild(saveButton);

        // Add event listeners
        const closeDialog = (confirmed, newFilename) => {
            dialogOverlay.remove();
            resolve({
                confirmed: confirmed,
                filename: confirmed ? (newFilename + '.png') : null
            });
        };

        closeButton.addEventListener('click', () => closeDialog(false));
        cancelButton.addEventListener('click', () => closeDialog(false));
        saveButton.addEventListener('click', () => {
            const newFilename = filenameInput.value.trim();
            if (!newFilename) {
                filenameInput.style.border = '1px solid red';
                filenameInput.focus();
                return;
            }
            closeDialog(true, newFilename);
        });

        // Handle ESC key
        dialogOverlay.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeDialog(false);
            } else if (e.key === 'Enter' && e.target === filenameInput) {
                saveButton.click();
            }
        });

        // Assemble dialog
        content.appendChild(imagePreview);
        content.appendChild(filenameContainer);

        dialog.appendChild(header);
        dialog.appendChild(content);
        dialog.appendChild(footer);
        dialogOverlay.appendChild(dialog);

        // Add to document
        document.body.appendChild(dialogOverlay);

        // Focus the filename input
        setTimeout(() => filenameInput.focus(), 100);
    });
}

/**
 * Shows a custom confirmation dialog for Drive uploads
 * @param {Object} storagePrefs - The storage preferences
 * @param {string} filename - The filename for the screenshot
 * @returns {Promise<boolean>} - Whether the user confirmed the upload
 */
async function showDriveUploadConfirmationDialog(storagePrefs, filename) {
    return new Promise(resolve => {
        // Create the confirmation dialog container
        const dialogOverlay = document.createElement('div');
        dialogOverlay.className = 'stickara-dialog-overlay';
        dialogOverlay.style.position = 'fixed';
        dialogOverlay.style.top = '0';
        dialogOverlay.style.left = '0';
        dialogOverlay.style.width = '100%';
        dialogOverlay.style.height = '100%';
        dialogOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        dialogOverlay.style.display = 'flex';
        dialogOverlay.style.justifyContent = 'center';
        dialogOverlay.style.alignItems = 'center';
        dialogOverlay.style.zIndex = '10000';

        // Create the dialog box
        const dialog = document.createElement('div');
        dialog.className = 'stickara-dialog';
        dialog.style.backgroundColor = 'white';
        dialog.style.borderRadius = '8px';
        dialog.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.2)';
        dialog.style.width = '400px';
        dialog.style.maxWidth = '90%';
        dialog.style.padding = '20px';
        dialog.style.animation = 'stickara-dialog-fade-in 0.2s ease-out';

        // Add animation styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes stickara-dialog-fade-in {
                from { opacity: 0; transform: translateY(-20px); }
                to { opacity: 1; transform: translateY(0); }
            }
        `;
        document.head.appendChild(style);

        // Create dialog content
        const title = document.createElement('h3');
        title.textContent = 'Save Screenshot to Google Drive';
        title.style.margin = '0 0 15px 0';
        title.style.fontSize = '18px';
        title.style.fontWeight = 'bold';
        title.style.color = '#333';

        const content = document.createElement('div');
        content.style.marginBottom = '20px';

        // Create info rows with icons
        const createInfoRow = (icon, text) => {
            const row = document.createElement('div');
            row.style.display = 'flex';
            row.style.alignItems = 'flex-start';
            row.style.marginBottom = '10px';

            const iconSpan = document.createElement('span');
            iconSpan.textContent = icon;
            iconSpan.style.marginRight = '10px';
            iconSpan.style.fontSize = '16px';
            iconSpan.style.width = '20px';
            iconSpan.style.textAlign = 'center';

            const textSpan = document.createElement('span');
            textSpan.innerHTML = text;
            textSpan.style.fontSize = '14px';
            textSpan.style.color = '#555';
            textSpan.style.flex = '1';

            row.appendChild(iconSpan);
            row.appendChild(textSpan);
            return row;
        };

        // Add info rows
        content.appendChild(createInfoRow('📄', `<strong>Filename:</strong> ${filename}`));
        content.appendChild(createInfoRow('📁', `<strong>Folder:</strong> ${storagePrefs.driveFolderName}`));
        content.appendChild(createInfoRow('🔒', `<strong>Visibility:</strong> ${storagePrefs.useAppDataFolder ? 'Private (only visible to this extension)' : 'Visible in your Google Drive'}`));

        if (storagePrefs.autoDeleteAfter > 0) {
            content.appendChild(createInfoRow('⏱️', `<strong>Auto-delete:</strong> After ${storagePrefs.autoDeleteAfter} days`));
        }

        // Create buttons
        const buttonContainer = document.createElement('div');
        buttonContainer.style.display = 'flex';
        buttonContainer.style.justifyContent = 'flex-end';
        buttonContainer.style.gap = '10px';

        const cancelButton = document.createElement('button');
        cancelButton.textContent = 'Cancel';
        cancelButton.style.padding = '8px 16px';
        cancelButton.style.border = '1px solid #ddd';
        cancelButton.style.borderRadius = '4px';
        cancelButton.style.backgroundColor = '#f5f5f5';
        cancelButton.style.color = '#333';
        cancelButton.style.cursor = 'pointer';
        cancelButton.style.fontSize = '14px';

        const saveButton = document.createElement('button');
        saveButton.textContent = 'Save to Drive';
        saveButton.style.padding = '8px 16px';
        saveButton.style.border = 'none';
        saveButton.style.borderRadius = '4px';
        saveButton.style.backgroundColor = '#4285f4';
        saveButton.style.color = 'white';
        saveButton.style.cursor = 'pointer';
        saveButton.style.fontSize = '14px';
        saveButton.style.fontWeight = 'bold';

        // Add event listeners
        cancelButton.addEventListener('click', () => {
            dialogOverlay.remove();
            style.remove();
            resolve(false);
        });

        saveButton.addEventListener('click', () => {
            dialogOverlay.remove();
            style.remove();
            resolve(true);
        });

        // Handle ESC key
        dialogOverlay.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                dialogOverlay.remove();
                style.remove();
                resolve(false);
            }
        });

        // Assemble dialog
        buttonContainer.appendChild(cancelButton);
        buttonContainer.appendChild(saveButton);

        dialog.appendChild(title);
        dialog.appendChild(content);
        dialog.appendChild(buttonContainer);
        dialogOverlay.appendChild(dialog);

        // Add to document
        document.body.appendChild(dialogOverlay);

        // Focus the save button
        saveButton.focus();
    });
}

/**
 * Shows a notification about where the screenshot is being stored
 * @param {Object} storagePrefs - The storage preferences
 */
function showStorageNotification(storagePrefs) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'stickara-notification';
    notification.style.position = 'fixed';
    notification.style.bottom = '20px';
    notification.style.right = '20px';
    notification.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    notification.style.color = 'white';
    notification.style.padding = '10px 15px';
    notification.style.borderRadius = '5px';
    notification.style.zIndex = '10000';
    notification.style.maxWidth = '300px';
    notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.3)';

    // Set notification message based on storage location
    let message = '';
    if (storagePrefs.storageLocation === 'local') {
        message = 'Screenshot will be saved locally';
    } else if (storagePrefs.storageLocation === 'drive') {
        message = `Screenshot will be saved to Google Drive in "${storagePrefs.driveFolderName}" folder`;
        if (storagePrefs.useAppDataFolder) {
            message += ' (private folder)';
        }
    } else if (storagePrefs.storageLocation === 'both') {
        message = `Screenshot will be saved locally and to Google Drive in "${storagePrefs.driveFolderName}" folder`;
        if (storagePrefs.useAppDataFolder) {
            message += ' (private folder)';
        }
    }

    // Add auto-deletion info if enabled
    if (storagePrefs.autoDeleteAfter > 0 && (storagePrefs.storageLocation === 'drive' || storagePrefs.storageLocation === 'both')) {
        message += `\nDrive screenshots will be auto-deleted after ${storagePrefs.autoDeleteAfter} days`;
    }

    notification.textContent = message;

    // Add to document and remove after delay
    document.body.appendChild(notification);
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transition = 'opacity 0.5s ease-out';
        setTimeout(() => notification.remove(), 500);
    }, 5000);
}

/**
 * Saves a screenshot locally
 * @param {string} imageDataUrl - The image data URL
 * @param {string} filename - The filename
 * @returns {Promise<void>}
 */
async function saveScreenshotLocally(imageDataUrl, filename) {
    return new Promise((resolve, reject) => {
        try {
            // Use the downloads API to save the file
            chrome.runtime.sendMessage({
                action: 'downloadScreenshot',
                imageDataUrl: imageDataUrl,
                filename: filename
            }, response => {
                if (response && response.success) {

                    resolve();
                } else {
                    console.error("SB Screenshot: Local save failed", response?.error);
                    reject(new Error(response?.error || 'Unknown error'));
                }
            });
        } catch (error) {
            console.error("SB Screenshot: Error saving locally:", error);
            reject(error);
        }
    });
}

/**
 * Schedules a screenshot for deletion after a specified number of days
 * @param {string} fileId - The Drive file ID
 * @param {number} days - The number of days after which to delete
 */
function scheduleScreenshotDeletion(fileId, days) {
    const deletionTime = Date.now() + (days * 24 * 60 * 60 * 1000);

    chrome.storage.local.get(['scheduledScreenshotDeletions'], result => {
        const deletions = result.scheduledScreenshotDeletions || [];
        deletions.push({
            fileId: fileId,
            deletionTime: deletionTime
        });

        chrome.storage.local.set({ scheduledScreenshotDeletions: deletions }, () => {
            console.log(`SB Screenshot: Scheduled deletion of ${fileId} after ${days} days`);
        });
    });
}

/** Inserts a simple text placeholder for a locally captured screenshot */
function insertLocalScreenshotPlaceholder(filenamePrefix) {
    // Ensure insertHtmlAtCursor exists and handles potential errors
    if (typeof insertHtmlAtCursor === 'function') {
        const placeholderHTML = `<p><em>[🖼️ Screenshot captured: ${filenamePrefix}]</em></p>`;
        insertHtmlAtCursor(placeholderHTML + '<br>'); // Add line break after
    } else {
        console.error("Stickara Error: insertHtmlAtCursor function not found. Cannot insert screenshot placeholder.");
        alert("Could not insert screenshot placeholder into the note.");
    }
}

// --- Public function (lives in content-interactions.js) ---
// function capturePageScreenshot(options = {}) { ... }

// --- Simple Selected Area Screenshot Functionality ---

let areaSelectionOverlay = null;
let isSelectingArea = false;
let selectionStartX = 0;
let selectionStartY = 0;
let selectionRect = null;
let selectionInfo = null;

/**
 * Starts the area selection mode for screenshot capture
 */
function startAreaSelection() {
    console.log("Stickara: Starting area selection mode");

    if (isSelectingArea) {
        console.warn("Stickara: Area selection already in progress");
        return;
    }

    // Close all open dropdowns before starting area selection to ensure clean capture
    if (typeof closeAllDropdowns === 'function') {
        closeAllDropdowns();
        console.log("Stickara Screenshot: Closed all dropdowns for clean area selection");
    }

    createAreaSelectionOverlay();
    isSelectingArea = true;

    // Show instructions
    showAreaSelectionInstructions();
}

/**
 * Creates the simple overlay for area selection
 */
function createAreaSelectionOverlay() {
    // Remove existing overlay if any
    if (areaSelectionOverlay) {
        areaSelectionOverlay.remove();
    }

    // Create main overlay
    areaSelectionOverlay = document.createElement('div');
    areaSelectionOverlay.id = 'stickara-area-selection-overlay';
    areaSelectionOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.3);
        z-index: 999999;
        cursor: crosshair;
        user-select: none;
    `;

    // Create simple selection rectangle
    selectionRect = document.createElement('div');
    selectionRect.id = 'stickara-selection-rect';
    selectionRect.style.cssText = `
        position: absolute;
        border: 2px solid #4285f4;
        background-color: rgba(66, 133, 244, 0.1);
        display: none;
        pointer-events: none;
        box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.8);
    `;

    // Create simple selection info display
    selectionInfo = document.createElement('div');
    selectionInfo.id = 'stickara-selection-info';
    selectionInfo.style.cssText = `
        position: absolute;
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        display: none;
        pointer-events: none;
        z-index: 1000001;
        font-family: monospace;
    `;

    areaSelectionOverlay.appendChild(selectionRect);
    areaSelectionOverlay.appendChild(selectionInfo);

    // Add event listeners
    areaSelectionOverlay.addEventListener('mousedown', startSelection);
    areaSelectionOverlay.addEventListener('mousemove', updateSelection);
    areaSelectionOverlay.addEventListener('mouseup', endSelection);
    areaSelectionOverlay.addEventListener('keydown', handleSelectionKeydown);

    // Make overlay focusable for keyboard events
    areaSelectionOverlay.tabIndex = -1;

    document.body.appendChild(areaSelectionOverlay);
    areaSelectionOverlay.focus();
}

/**
 * Shows simple instructions for area selection
 */
function showAreaSelectionInstructions() {
    const instructions = document.createElement('div');
    instructions.id = 'stickara-selection-instructions';
    instructions.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        font-size: 14px;
        z-index: 1000000;
        pointer-events: none;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
    instructions.textContent = 'Drag to select an area for screenshot. Press ESC to cancel.';

    document.body.appendChild(instructions);

    // Remove instructions after 3 seconds
    setTimeout(() => {
        if (instructions.parentNode) {
            instructions.remove();
        }
    }, 3000);
}







/**
 * Handles mouse down event to start selection
 */
function startSelection(e) {
    e.preventDefault();
    selectionStartX = e.clientX;
    selectionStartY = e.clientY;

    selectionRect.style.left = selectionStartX + 'px';
    selectionRect.style.top = selectionStartY + 'px';
    selectionRect.style.width = '0px';
    selectionRect.style.height = '0px';
    selectionRect.style.display = 'block';

    // Show selection info
    selectionInfo.style.display = 'block';
}

/**
 * Handles mouse move event to update selection
 */
function updateSelection(e) {
    if (!selectionRect || selectionRect.style.display === 'none') return;

    const currentX = e.clientX;
    const currentY = e.clientY;

    const left = Math.min(selectionStartX, currentX);
    const top = Math.min(selectionStartY, currentY);
    const width = Math.abs(currentX - selectionStartX);
    const height = Math.abs(currentY - selectionStartY);

    selectionRect.style.left = left + 'px';
    selectionRect.style.top = top + 'px';
    selectionRect.style.width = width + 'px';
    selectionRect.style.height = height + 'px';

    // Update selection info display
    updateSelectionInfo(width, height, left, top);
}

/**
 * Updates the selection info display with real-time dimensions
 */
function updateSelectionInfo(width, height, left, top) {
    if (!selectionInfo) return;

    const pixelWidth = Math.round(width);
    const pixelHeight = Math.round(height);

    selectionInfo.innerHTML = `${pixelWidth} × ${pixelHeight}px`;

    // Position info box near selection
    const infoX = left + width + 10;
    const infoY = top - 30;

    // Keep info box within viewport
    const maxX = window.innerWidth - 150;
    const maxY = window.innerHeight - 50;

    selectionInfo.style.left = Math.min(infoX, maxX) + 'px';
    selectionInfo.style.top = Math.max(Math.min(infoY, maxY), 10) + 'px';
}

/**
 * Handles mouse up event to end selection
 */
function endSelection(e) {
    if (!selectionRect || selectionRect.style.display === 'none') return;

    const rect = selectionRect.getBoundingClientRect();

    // Check if selection is large enough (minimum 10x10 pixels)
    if (rect.width < 10 || rect.height < 10) {
        cancelAreaSelection();
        showStatus('Selection too small. Please select a larger area.', 'error');
        return;
    }

    // Calculate selection coordinates relative to the page
    const selectionData = {
        x: rect.left + window.scrollX,
        y: rect.top + window.scrollY,
        width: rect.width,
        height: rect.height,
        scrollX: window.scrollX,
        scrollY: window.scrollY,
        viewportWidth: window.innerWidth,
        viewportHeight: window.innerHeight
    };

    console.log("Stickara: Area selected:", selectionData);

    // CRITICAL: Remove overlay first, then capture after delay to ensure clean content
    hideOverlayAndCapture(selectionData);
}

/**
 * Hides the selection overlay and captures after ensuring clean content visibility
 */
function hideOverlayAndCapture(selectionData) {
    console.log("Stickara: Hiding overlay before capture to ensure clean content");

    // Step 1: Hide the overlay immediately but keep reference for cleanup
    if (areaSelectionOverlay) {
        areaSelectionOverlay.style.display = 'none';
        areaSelectionOverlay.style.visibility = 'hidden';
        areaSelectionOverlay.style.opacity = '0';
    }

    // Step 2: Force a repaint to ensure overlay is completely hidden
    document.body.offsetHeight; // Force reflow

    // Step 3: Wait for next animation frame to ensure rendering is complete
    requestAnimationFrame(() => {
        // Step 4: Additional small delay to ensure browser has fully rendered clean content
        setTimeout(() => {
            console.log("Stickara: Overlay hidden, capturing clean content now");

            // Step 5: Now capture the clean content
            captureSelectedArea(selectionData);

            // Step 6: Clean up overlay after capture is initiated
            setTimeout(() => {
                cleanupAreaSelection();
            }, 100);

        }, 50); // 50ms delay to ensure clean rendering
    });
}

/**
 * Handles keyboard events during selection
 */
function handleSelectionKeydown(e) {
    if (e.key === 'Escape') {
        e.preventDefault();
        cancelAreaSelection();
    }
}



/**
 * Cancels area selection and ensures clean content restoration
 */
function cancelAreaSelection() {
    console.log("Stickara: Area selection cancelled, restoring clean content");

    // Immediately hide overlay to restore clean content
    if (areaSelectionOverlay) {
        areaSelectionOverlay.style.display = 'none';
        areaSelectionOverlay.style.visibility = 'hidden';
        areaSelectionOverlay.style.opacity = '0';
    }

    // Force repaint to ensure clean content is visible
    document.body.offsetHeight;

    // Clean up after ensuring content is restored
    setTimeout(() => {
        cleanupAreaSelection();
        showStatus('Screenshot capture cancelled', 'info');
    }, 25);
}



/**
 * Cleans up area selection overlay and state
 */
function cleanupAreaSelection() {
    isSelectingArea = false;

    if (areaSelectionOverlay) {
        areaSelectionOverlay.remove();
        areaSelectionOverlay = null;
    }

    selectionRect = null;
    selectionInfo = null;

    // Remove instructions if still visible
    const instructions = document.getElementById('stickara-selection-instructions');
    if (instructions) {
        instructions.remove();
    }
}

/**
 * Captures the selected area with clean content (no overlay artifacts)
 */
function captureSelectedArea(selectionData) {
    console.log("Stickara: Capturing selected area with clean content");

    // Verify overlay is completely hidden before proceeding
    if (areaSelectionOverlay && areaSelectionOverlay.style.display !== 'none') {
        console.warn("Stickara: Overlay still visible, forcing hide before capture");
        areaSelectionOverlay.style.display = 'none';
        areaSelectionOverlay.style.visibility = 'hidden';
        areaSelectionOverlay.style.opacity = '0';

        // Force another repaint and retry
        document.body.offsetHeight;
        setTimeout(() => captureSelectedArea(selectionData), 25);
        return;
    }

    // Get other screenshot options
    const downloadCheckbox = document.getElementById('Stickara-screenshot-option-download');
    const hideUiCheckbox = document.getElementById('Stickara-screenshot-option-hide-ui');

    const options = {
        captureMode: 'selectedArea',
        actionAfterCapture: (downloadCheckbox?.checked ?? false) ? 'download' : 'annotate',
        hideStickara: hideUiCheckbox?.checked ?? false,
        quality: 100,
        selectionData: selectionData
    };

    console.log("Stickara: Initiating clean selected area capture with options:", options);
    showStatus('Capturing clean selected area...', 'info');

    // Call the existing capture function with clean content
    capturePageScreenshot(options);
}

/**
 * Crops an image to the selected area using high-quality Canvas
 * @param {string} imageDataUrl - The original image data URL
 * @param {Object} selectionData - The selection coordinates and metadata
 * @returns {Promise<string>} - The cropped image data URL
 */
async function cropImageToSelection(imageDataUrl, selectionData) {
    return new Promise((resolve, reject) => {
        try {
            console.log("Stickara: Starting high-quality image cropping with selection data:", selectionData);

            // Create an image element to load the original image
            const img = new Image();

            img.onload = function() {
                try {
                    // Calculate the crop coordinates relative to the captured image
                    // The selection coordinates are relative to the viewport, but we need them relative to the image
                    const scaleX = img.width / selectionData.viewportWidth;
                    const scaleY = img.height / selectionData.viewportHeight;

                    // Calculate crop area (selection coordinates relative to viewport)
                    let cropX = (selectionData.x - selectionData.scrollX) * scaleX;
                    let cropY = (selectionData.y - selectionData.scrollY) * scaleY;
                    let cropWidth = selectionData.width * scaleX;
                    let cropHeight = selectionData.height * scaleY;

                    console.log("Stickara: Crop coordinates:", { cropX, cropY, cropWidth, cropHeight });
                    console.log("Stickara: Image dimensions:", { width: img.width, height: img.height });
                    console.log("Stickara: Scale factors:", { scaleX, scaleY });

                    // Validate crop coordinates
                    if (cropX < 0 || cropY < 0 || cropWidth <= 0 || cropHeight <= 0 ||
                        cropX + cropWidth > img.width || cropY + cropHeight > img.height) {
                        console.warn("Stickara: Invalid crop coordinates, adjusting...");

                        // Adjust coordinates to be within image bounds
                        const adjustedCropX = Math.max(0, Math.min(cropX, img.width - 1));
                        const adjustedCropY = Math.max(0, Math.min(cropY, img.height - 1));
                        const adjustedCropWidth = Math.min(cropWidth, img.width - adjustedCropX);
                        const adjustedCropHeight = Math.min(cropHeight, img.height - adjustedCropY);

                        console.log("Stickara: Adjusted crop coordinates:", {
                            cropX: adjustedCropX,
                            cropY: adjustedCropY,
                            cropWidth: adjustedCropWidth,
                            cropHeight: adjustedCropHeight
                        });

                        // Use adjusted coordinates
                        cropX = adjustedCropX;
                        cropY = adjustedCropY;
                        cropWidth = adjustedCropWidth;
                        cropHeight = adjustedCropHeight;
                    }

                    // Create a high-quality canvas for cropping
                    const canvas = document.createElement('canvas');
                    canvas.width = cropWidth;
                    canvas.height = cropHeight;
                    const ctx = canvas.getContext('2d');

                    if (!ctx) {
                        throw new Error("Failed to get 2D context from canvas");
                    }

                    // Enable high-quality image rendering
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';

                    // Draw the cropped portion of the image with high quality
                    ctx.drawImage(
                        img,
                        cropX, cropY, cropWidth, cropHeight,  // Source rectangle
                        0, 0, cropWidth, cropHeight           // Destination rectangle
                    );

                    // Convert to high-quality PNG data URL
                    const croppedImageDataUrl = canvas.toDataURL('image/png', 1.0);
                    console.log("Stickara: High-quality image cropping completed successfully");
                    resolve(croppedImageDataUrl);

                } catch (error) {
                    console.error("Stickara: Error during image cropping:", error);
                    reject(new Error("Image cropping failed: " + error.message));
                }
            };

            img.onerror = function() {
                console.error("Stickara: Failed to load image for cropping");
                reject(new Error("Failed to load image for cropping"));
            };

            // Start loading the image
            img.src = imageDataUrl;

        } catch (error) {
            console.error("Stickara: Error setting up image cropping:", error);
            reject(new Error("Failed to setup image cropping: " + error.message));
        }
    });
}

/**
 * Captures a high-resolution screenshot of a video element at its current timestamp.
 * Creates a canvas-based screenshot that captures only the video content with maximum quality.
 * @param {HTMLVideoElement} videoElement - The video element to capture
 * @param {string} formattedTime - The formatted timestamp for filename/display
 * @param {HTMLElement} insertAfterElement - Element to insert the screenshot after (optional)
 * @returns {string} The captured image data URL
 */
async function captureVideoScreenshot(videoElement, formattedTime, insertAfterElement = null) {
    try {
        console.log("Stickara: Starting high-quality video screenshot capture...");

        // Get native video dimensions for maximum quality
        let videoWidth = videoElement.videoWidth || videoElement.clientWidth;
        let videoHeight = videoElement.videoHeight || videoElement.clientHeight;

        // Ensure minimum dimensions for very small videos
        if (videoWidth < 100 || videoHeight < 100) {
            const aspectRatio = videoWidth / videoHeight;
            if (videoWidth < videoHeight) {
                videoWidth = 480;
                videoHeight = Math.round(480 / aspectRatio);
            } else {
                videoHeight = 360;
                videoWidth = Math.round(360 * aspectRatio);
            }
        }

        // Cap maximum dimensions to prevent memory issues while maintaining quality
        const maxDimension = 3840; // 4K width
        if (videoWidth > maxDimension || videoHeight > maxDimension) {
            const aspectRatio = videoWidth / videoHeight;
            if (videoWidth > videoHeight) {
                videoWidth = maxDimension;
                videoHeight = Math.round(maxDimension / aspectRatio);
            } else {
                videoHeight = maxDimension;
                videoWidth = Math.round(maxDimension * aspectRatio);
            }
        }

        console.log(`Stickara: Video optimized dimensions - ${videoWidth}x${videoHeight}`);

        // Create a high-resolution canvas matching video's native resolution
        const canvas = document.createElement('canvas');
        canvas.width = videoWidth;
        canvas.height = videoHeight;
        const ctx = canvas.getContext('2d', {
            alpha: false, // No transparency for better compression
            desynchronized: true, // Better performance
            willReadFrequently: true // Enable for multiple readback operations (fixes Canvas2D warning)
        });

        if (!ctx) {
            throw new Error("Failed to get 2D context from canvas");
        }

        // Enable maximum quality rendering
        ctx.imageSmoothingEnabled = false; // Disable smoothing for pixel-perfect capture
        ctx.globalCompositeOperation = 'source-over';

        // Draw the current video frame to the canvas at native resolution
        ctx.drawImage(videoElement, 0, 0, videoWidth, videoHeight);

        // Capture at maximum quality without compression
        const videoImageDataUrl = canvas.toDataURL(VIDEO_SCREENSHOT_FORMAT, 1.0);
        const imageSizeKB = Math.round((videoImageDataUrl.length * 0.75) / 1024);

        console.log(`Stickara: High-quality video screenshot captured - ${imageSizeKB}KB (uncompressed ${VIDEO_SCREENSHOT_FORMAT})`);

        // Directly insert the screenshot into the note
        await insertVideoScreenshotDirectly(videoImageDataUrl, formattedTime, insertAfterElement);

        return videoImageDataUrl;

    } catch (error) {
        console.error("Stickara: Error capturing video screenshot:", error);
        throw new Error("Video screenshot capture failed: " + error.message);
    }
}

/**
 * Directly inserts the video screenshot into the note after the timestamp element.
 * @param {string} imageDataUrl - The captured video screenshot as data URL
 * @param {string} formattedTime - The formatted timestamp for filename/display
 * @param {HTMLElement} insertAfterElement - Element to insert the screenshot after
 */
async function insertVideoScreenshotDirectly(imageDataUrl, formattedTime, insertAfterElement = null) {
    try {
        if (!noteText) {
            throw new Error("Note text area not found");
        }

        // Check image size for high-quality storage
        const imageSizeKB = Math.round((imageDataUrl.length * 0.75) / 1024);
        const maxEmbedSizeKB = (typeof VIDEO_SCREENSHOT_MAX_EMBED_SIZE_KB !== 'undefined') ? VIDEO_SCREENSHOT_MAX_EMBED_SIZE_KB : 8192; // 8MB limit for high-quality storage

        console.log(`Stickara: Video screenshot size for embedding: ${imageSizeKB}KB (limit: ${maxEmbedSizeKB}KB)`);

        if (imageSizeKB > maxEmbedSizeKB) {
            console.warn(`Stickara: Video screenshot too large for direct embedding (${imageSizeKB}KB > ${maxEmbedSizeKB}KB)`);

            // Instead of embedding, create a placeholder and offer download
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
            const uniqueId = `video-screenshot-placeholder-${timestamp}-${Math.random().toString(36).substr(2, 9)}`;

            const placeholderHtml = `<p><div
                id="${uniqueId}"
                class="Stickara-video-screenshot-placeholder"
                data-timestamp="${formattedTime}"
                data-image-data="${imageDataUrl}"
                style="border: 2px dashed #007bff; border-radius: 8px; padding: 20px; margin: 10px 0; text-align: center; background: #f8f9ff; cursor: pointer; transition: all 0.2s ease;"
                title="Video Screenshot at ${formattedTime} - Click for options">
                <div style="color: #007bff; font-weight: bold; margin-bottom: 8px;">📹 Video Screenshot (${formattedTime})</div>
                <div style="color: #666; font-size: 12px;">Size: ${imageSizeKB}KB - Click to Save or Edit</div>
            </div></p>`;

            // Insert placeholder
            if (insertAfterElement) {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = placeholderHtml;
                const placeholderElement = tempDiv.firstChild;
                insertAfterElement.parentNode.insertBefore(placeholderElement, insertAfterElement.nextSibling);
            } else {
                if (typeof insertHtmlAtCursor === 'function') {
                    insertHtmlAtCursor(placeholderHtml);
                } else {
                    document.execCommand('insertHTML', false, placeholderHtml);
                }
            }

            // Add click event listener to the placeholder
            setTimeout(() => {
                const insertedPlaceholder = document.getElementById(uniqueId);
                if (insertedPlaceholder) {
                    insertedPlaceholder.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        showVideoScreenshotClickOptions(insertedPlaceholder);
                    });

                    // Add hover effects
                    insertedPlaceholder.addEventListener('mouseenter', () => {
                        insertedPlaceholder.style.borderColor = '#0056b3';
                        insertedPlaceholder.style.backgroundColor = '#e7f3ff';
                    });

                    insertedPlaceholder.addEventListener('mouseleave', () => {
                        insertedPlaceholder.style.borderColor = '#007bff';
                        insertedPlaceholder.style.backgroundColor = '#f8f9ff';
                    });
                }
            }, 100);

            if (typeof showStatus === 'function') {
                showStatus(`Video screenshot placeholder inserted (${imageSizeKB}KB) - Click to access`, 'info');
            }

            return;
        }

        // Create timestamp for alt text and unique ID
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const uniqueId = `video-screenshot-${timestamp}-${Math.random().toString(36).substr(2, 9)}`;
        const altText = `Video Screenshot at ${formattedTime} - ${timestamp}`;

        // Create HTML for the embedded image with clean, static styling
        const imageHtml = `<p><img
            src="${imageDataUrl}"
            alt="${altText}"
            id="${uniqueId}"
            class="Stickara-video-screenshot"
            data-timestamp="${formattedTime}"
            data-image-data="${imageDataUrl}"
            loading="lazy"
            decoding="async"
            style="max-width: 100%; height: auto; border: 2px solid #007bff; border-radius: 8px; margin: 10px 0; display: block; cursor: pointer;"
            title="Video Screenshot at ${formattedTime} - Click for options"
            contenteditable="false"></p>`;

        // Focus the note text area
        noteText.focus();

        if (insertAfterElement) {
            // Insert after the specific element (timestamp)
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = imageHtml;
            const imageElement = tempDiv.firstChild;

            // Insert after the timestamp element
            insertAfterElement.parentNode.insertBefore(imageElement, insertAfterElement.nextSibling);
        } else {
            // Insert at cursor position
            if (typeof insertHtmlAtCursor === 'function') {
                insertHtmlAtCursor(imageHtml);
            } else {
                // Fallback method
                document.execCommand('insertHTML', false, imageHtml);
            }
        }

        // Add click event listener to the inserted image with clean, static behavior
        requestIdleCallback(() => {
            const insertedImage = document.getElementById(uniqueId);
            if (insertedImage) {
                // Add only click functionality - no hover effects
                insertedImage.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    showVideoScreenshotClickOptions(insertedImage);
                }, { passive: false });

                // Add to performance monitoring with deferred execution
                requestIdleCallback(() => {
                    trackVideoScreenshot(uniqueId, imageSizeKB);
                });
            }
        }, { timeout: 1000 });

        // Schedule save without compression and monitor performance
        if (typeof scheduleSave === 'function') {
            // Use requestIdleCallback for non-blocking save
            requestIdleCallback(() => {
                scheduleSave();
                // Log storage usage for monitoring
                setTimeout(() => logStorageUsage(), 1000);

                // Monitor note performance after adding screenshot
                monitorNotePerformance();
            }, { timeout: 2000 });
        }

        if (typeof showStatus === 'function') {
            showStatus(`Video screenshot inserted at ${formattedTime}`, 'success');
        }

        console.log(`Stickara: Video screenshot inserted directly at ${formattedTime}`);
    } catch (error) {
        console.error("Stickara: Error inserting video screenshot:", error);
        if (typeof showStatus === 'function') {
            showStatus('Failed to insert video screenshot', 'error');
        }
    }
}

/**
 * Shows minimalistic click options when user clicks on an inserted video screenshot.
 * @param {HTMLImageElement} imageElement - The clicked video screenshot image
 */
function showVideoScreenshotClickOptions(imageElement) {
    const imageDataUrl = imageElement.getAttribute('data-image-data');
    const timestamp = imageElement.getAttribute('data-timestamp');

    if (!imageDataUrl || !timestamp) {
        console.error("Stickara: Missing image data or timestamp");
        return;
    }

    // Create an improved, sleek options menu
    const optionsMenu = document.createElement('div');
    optionsMenu.id = 'Stickara-video-screenshot-options';
    optionsMenu.style.cssText = `
        position: fixed;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        padding: 6px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 999999;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        display: flex;
        gap: 4px;
        min-width: 140px;
        animation: slideIn 0.2s ease-out;
    `;

    // Add CSS animation
    if (!document.getElementById('stickara-screenshot-options-style')) {
        const style = document.createElement('style');
        style.id = 'stickara-screenshot-options-style';
        style.textContent = `
            @keyframes slideIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }
        `;
        document.head.appendChild(style);
    }

    // Position the menu near the image
    const rect = imageElement.getBoundingClientRect();
    optionsMenu.style.left = Math.min(rect.left, window.innerWidth - 130) + 'px';
    optionsMenu.style.top = (rect.bottom + 5) + 'px';

    // Create improved Save button
    const saveBtn = document.createElement('button');
    saveBtn.innerHTML = '💾 Save';
    saveBtn.style.cssText = `
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 6px;
        padding: 6px 10px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        flex: 1;
        min-height: 28px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    `;

    // Create improved Edit button
    const editBtn = document.createElement('button');
    editBtn.innerHTML = '✏️ Edit';
    editBtn.style.cssText = `
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 6px;
        padding: 6px 10px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        flex: 1;
        min-height: 28px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    `;

    // Create improved Crop button
    const cropBtn = document.createElement('button');
    cropBtn.innerHTML = '✂️ Crop';
    cropBtn.style.cssText = `
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 6px;
        padding: 6px 10px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        flex: 1;
        min-height: 28px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    `;

    // Add enhanced hover effects
    const addHoverEffect = (btn) => {
        btn.addEventListener('mouseenter', () => {
            btn.style.background = 'linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%)';
            btn.style.transform = 'translateY(-1px)';
            btn.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
        });
        btn.addEventListener('mouseleave', () => {
            btn.style.background = 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)';
            btn.style.transform = 'translateY(0)';
            btn.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
        });
    };

    addHoverEffect(saveBtn);
    addHoverEffect(editBtn);
    addHoverEffect(cropBtn);

    // Add event listeners
    saveBtn.addEventListener('click', async () => {
        await downloadVideoScreenshot(imageDataUrl, timestamp);
        closeVideoScreenshotOptions();
    });

    editBtn.addEventListener('click', async () => {
        await openVideoScreenshotEditor(imageDataUrl, timestamp, imageElement);
        closeVideoScreenshotOptions();
    });

    cropBtn.addEventListener('click', async () => {
        await openVideoScreenshotCropper(imageDataUrl, timestamp, imageElement);
        closeVideoScreenshotOptions();
    });

    // Assemble menu with all three options
    optionsMenu.appendChild(saveBtn);
    optionsMenu.appendChild(editBtn);
    optionsMenu.appendChild(cropBtn);
    document.body.appendChild(optionsMenu);

    // Close menu when clicking outside
    const closeOnOutsideClick = (e) => {
        if (!optionsMenu.contains(e.target) && e.target !== imageElement) {
            closeVideoScreenshotOptions();
            document.removeEventListener('click', closeOnOutsideClick);
        }
    };

    setTimeout(() => {
        document.addEventListener('click', closeOnOutsideClick);
    }, 100);

    // Close on Escape key
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            closeVideoScreenshotOptions();
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);
}

/**
 * Closes the video screenshot options menu.
 */
function closeVideoScreenshotOptions() {
    const optionsMenu = document.getElementById('Stickara-video-screenshot-options');
    if (optionsMenu && optionsMenu.parentNode) {
        optionsMenu.parentNode.removeChild(optionsMenu);
    }
}

/**
 * Opens the video screenshot in the annotation editor.
 * @param {string} imageDataUrl - The image data URL
 * @param {string} timestamp - The timestamp
 */
async function openVideoScreenshotEditor(imageDataUrl, timestamp, originalImageElement = null) {
    try {
        console.log("Stickara: Opening video screenshot editor with timestamp:", timestamp);

        if (!imageDataUrl) {
            throw new Error("No image data URL provided");
        }

        // Validate that the image data URL is valid
        if (!imageDataUrl.startsWith('data:image/')) {
            throw new Error("Invalid image data URL format");
        }

        // Check if the annotation modal function exists
        if (typeof openScreenshotAnnotationModal !== 'function') {
            throw new Error("Screenshot annotation modal function not available");
        }

        // Check image size for high-quality editor support
        const imageSizeKB = Math.round((imageDataUrl.length * 0.75) / 1024);
        const maxEditorSizeKB = 15360; // 15MB limit for high-quality editor support

        console.log(`Stickara: Opening editor with ${imageSizeKB}KB image (limit: ${maxEditorSizeKB}KB)`);

        if (imageSizeKB > maxEditorSizeKB) {
            console.warn(`Stickara: Video screenshot is very large (${imageSizeKB}KB), may affect performance`);

            // Offer information about large file
            const proceed = confirm(
                `This video screenshot is very large (${imageSizeKB}KB).\n\n` +
                `Large images may take longer to load in the editor.\n\n` +
                `Continue with editor?`
            );

            if (!proceed) {
                // User chose not to proceed, download instead
                await downloadVideoScreenshot(imageDataUrl, timestamp);
                return;
            }
        }

        // Clear any existing annotations to start fresh
        if (typeof annotations !== 'undefined') {
            annotations = [];
        }

        // Store reference to original image for in-place editing
        if (originalImageElement) {
            window._stickaraEditingContext = {
                originalImage: originalImageElement,
                timestamp: timestamp,
                isInPlaceEdit: true
            };
        } else {
            window._stickaraEditingContext = {
                isInPlaceEdit: false
            };
        }

        // Directly open the annotation modal with the image data
        openScreenshotAnnotationModal(imageDataUrl);

        // Wait for the modal to initialize, then update the filename and UI
        setTimeout(() => {
            try {
                const filenameInput = document.querySelector('#Stickara-screenshot-filename');
                if (filenameInput) {
                    const timestamp_clean = timestamp.replace(/:/g, '-');
                    filenameInput.value = `video-screenshot-${timestamp_clean}`;
                    console.log("Stickara: Updated filename for video screenshot");
                }

                // Update the modal title to indicate it's a video screenshot
                const modalTitle = document.querySelector('#Stickara-screenshot-modal h4');
                if (modalTitle) {
                    if (originalImageElement) {
                        modalTitle.textContent = `Edit Video Screenshot (${timestamp})`;
                    } else {
                        modalTitle.textContent = `Annotate Video Screenshot (${timestamp})`;
                    }
                    console.log("Stickara: Updated modal title");
                } else {
                    console.warn("Stickara: Modal title not found");
                }

                if (typeof showStatus === 'function') {
                    const action = originalImageElement ? 'editor' : 'annotation';
                    showStatus(`Video screenshot ${action} opened for ${timestamp}`, 'success');
                }
            } catch (updateError) {
                console.warn("Stickara: Error updating editor UI:", updateError);
                // Don't throw here, the editor is still functional
            }
        }, 300);

        // Modify the save button behavior for in-place editing (with longer delay)
        if (originalImageElement) {
            setTimeout(() => {
                setupInPlaceEditingSaveButton();
            }, 600);
        }

        console.log("Stickara: Video screenshot editor opened successfully");

    } catch (error) {
        console.error("Stickara: Error opening video screenshot editor:", error);

        if (typeof showStatus === 'function') {
            showStatus(`Failed to open editor: ${error.message}. Downloading instead.`, 'warning');
        }

        // Fallback: just download the image
        await downloadVideoScreenshot(imageDataUrl, timestamp);
    }
}

/**
 * Sets up the save button for in-place editing functionality.
 */
function setupInPlaceEditingSaveButton() {
    try {
        console.log("Stickara: Setting up in-place editing buttons...");

        // Wait a bit more for the modal to be fully rendered
        setTimeout(() => {
            // Find buttons by their content/title since they don't have specific IDs
            const modalButtons = document.querySelectorAll('#Stickara-screenshot-modal button');
            let saveButton = null;
            let insertButton = null;

            modalButtons.forEach(button => {
                const title = button.title || button.getAttribute('title') || '';
                const text = button.textContent || button.innerText || '';

                console.log(`Stickara: Found button - Title: "${title}", Text: "${text}"`);

                if (title.includes('Save Screenshot') || text.includes('💾')) {
                    saveButton = button;
                    console.log("Stickara: Found save button");
                } else if (title.includes('Insert into Note') || text.includes('📝')) {
                    insertButton = button;
                    console.log("Stickara: Found insert button");
                }
            });

            if (saveButton) {
                // Replace the save button's click handler
                const newSaveButton = saveButton.cloneNode(true);
                saveButton.parentNode.replaceChild(newSaveButton, saveButton);

                // Update button text and styling for in-place editing
                newSaveButton.innerHTML = '✅ Apply Changes';
                newSaveButton.title = 'Apply changes to the original image in the note';

                // Add distinctive styling with better contrast
                newSaveButton.style.cssText = `
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                    color: white;
                    font-weight: 600;
                    font-size: 13px;
                    border: 2px solid #1e7e34;
                    border-radius: 6px;
                    padding: 8px 12px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                `;

                // Add improved hover effects with better readability
                newSaveButton.addEventListener('mouseenter', () => {
                    newSaveButton.style.background = 'linear-gradient(135deg, #218838 0%, #1c7430 100%)';
                    newSaveButton.style.transform = 'translateY(-1px)';
                    newSaveButton.style.boxShadow = '0 4px 8px rgba(40, 167, 69, 0.4)';
                    newSaveButton.style.borderColor = '#155724';
                });

                newSaveButton.addEventListener('mouseleave', () => {
                    newSaveButton.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
                    newSaveButton.style.transform = 'translateY(0)';
                    newSaveButton.style.boxShadow = '0 2px 4px rgba(40, 167, 69, 0.3)';
                    newSaveButton.style.borderColor = '#1e7e34';
                });

                // Add click animation
                newSaveButton.addEventListener('mousedown', () => {
                    newSaveButton.style.transform = 'translateY(1px)';
                    newSaveButton.style.boxShadow = '0 1px 2px rgba(40, 167, 69, 0.3)';
                });

                newSaveButton.addEventListener('mouseup', () => {
                    newSaveButton.style.transform = 'translateY(-1px)';
                    newSaveButton.style.boxShadow = '0 4px 8px rgba(40, 167, 69, 0.4)';
                });

                newSaveButton.addEventListener('click', handleInPlaceImageUpdate);
                console.log("Stickara: Save button updated for in-place editing with improved styling");
            } else {
                console.warn("Stickara: Save button not found in annotation modal");
            }

            // Hide the insert button since we're updating in-place
            if (insertButton) {
                insertButton.style.display = 'none';
                console.log("Stickara: Insert button hidden for in-place editing");
            } else {
                console.warn("Stickara: Insert button not found in annotation modal");
            }

            console.log("Stickara: In-place editing button setup completed");

        }, 500); // Increased delay to ensure modal is fully rendered

    } catch (error) {
        console.error("Stickara: Error setting up in-place editing save button:", error);
    }
}

/**
 * Handles updating the original image in-place with the edited version.
 */
async function handleInPlaceImageUpdate() {
    try {
        const editingContext = window._stickaraEditingContext;

        if (!editingContext || !editingContext.isInPlaceEdit || !editingContext.originalImage) {
            throw new Error("No valid editing context for in-place update");
        }

        // Get the edited image data from the canvas
        if (!screenshotCanvas || !screenshotCtx) {
            throw new Error("Screenshot canvas not available");
        }

        // Optimize canvas for frequent readback operations
        if (screenshotCanvas.getContext && !screenshotCanvas._optimizedForReadback) {
            const ctx = screenshotCanvas.getContext('2d', { willReadFrequently: true });
            screenshotCanvas._optimizedForReadback = true;
            console.log("Stickara: Canvas optimized for frequent readback operations");
        }

        // Ensure final drawing state is on the canvas
        redrawScreenshotCanvas();

        // Get the edited image as data URL
        const editedImageDataUrl = screenshotCanvas.toDataURL('image/png', 1.0);

        if (!editedImageDataUrl || editedImageDataUrl === 'data:,') {
            throw new Error("Failed to generate edited image data");
        }

        // Update the original image element
        const originalImage = editingContext.originalImage;

        // Preserve original attributes
        const originalTimestamp = originalImage.getAttribute('data-timestamp');
        const originalId = originalImage.id;
        const originalClass = originalImage.className;
        const originalStyle = originalImage.getAttribute('style');
        const originalTitle = originalImage.title;

        // Update the image source with the edited version
        originalImage.src = editedImageDataUrl;
        originalImage.setAttribute('data-image-data', editedImageDataUrl);

        // Update the title to indicate it was edited
        const timestamp = editingContext.timestamp;
        originalImage.title = `Video Screenshot at ${timestamp} - Click for options (Edited)`;

        // Preserve all original styling and functionality
        originalImage.id = originalId;
        originalImage.className = originalClass;
        originalImage.setAttribute('style', originalStyle);
        originalImage.setAttribute('data-timestamp', originalTimestamp);

        // Ensure click functionality is maintained
        if (!originalImage.hasAttribute('data-click-handler-attached')) {
            originalImage.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                showVideoScreenshotClickOptions(originalImage);
            }, { passive: false });
            originalImage.setAttribute('data-click-handler-attached', 'true');
        }

        // Close the annotation modal
        closeScreenshotAnnotationModal();

        // Clear editing context
        window._stickaraEditingContext = null;

        // Show success message
        if (typeof showStatus === 'function') {
            showStatus(`Video screenshot updated in-place for ${timestamp}`, 'success');
        }

        // Trigger save if available
        if (typeof scheduleSave === 'function') {
            requestIdleCallback(() => {
                scheduleSave();
            });
        }

        console.log(`Stickara: Successfully updated video screenshot in-place for ${timestamp}`);

    } catch (error) {
        console.error("Stickara: Error updating image in-place:", error);

        if (typeof showStatus === 'function') {
            showStatus(`Failed to update image: ${error.message}`, 'error');
        }

        // Fallback: close modal and show error
        closeScreenshotAnnotationModal();
        alert(`Failed to update image in-place: ${error.message}\n\nPlease try again.`);
    }
}

/**
 * Downloads the video screenshot as a PNG file with improved filename.
 * @param {string} imageDataUrl - The screenshot data URL
 * @param {string} formattedTime - The formatted timestamp
 */
async function downloadVideoScreenshot(imageDataUrl, formattedTime) {
    try {
        // Generate filename with timestamp and current date
        const currentDate = new Date();
        const dateStr = currentDate.toISOString().slice(0, 10); // YYYY-MM-DD
        const timeStr = formattedTime.replace(/:/g, '-');
        const filename = `video-screenshot-${timeStr}-${dateStr}.png`;

        // Create download link
        const link = document.createElement('a');
        link.href = imageDataUrl;
        link.download = filename;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        if (typeof showStatus === 'function') {
            showStatus(`Video screenshot downloaded: ${filename}`, 'success');
        }

        console.log(`Stickara: Video screenshot downloaded as ${filename}`);
    } catch (error) {
        console.error("Stickara: Error downloading video screenshot:", error);
        if (typeof showStatus === 'function') {
            showStatus('Failed to download video screenshot', 'error');
        }
    }
}

/**
 * Opens the video screenshot cropper interface.
 * @param {string} imageDataUrl - The image data URL
 * @param {string} timestamp - The timestamp
 */
async function openVideoScreenshotCropper(imageDataUrl, timestamp, originalImageElement = null) {
    try {
        console.log("Stickara: Opening video screenshot cropper with timestamp:", timestamp);

        if (!imageDataUrl) {
            throw new Error("No image data URL provided");
        }

        // Create cropper modal
        const cropperModal = document.createElement('div');
        cropperModal.id = 'Stickara-video-screenshot-cropper';
        cropperModal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 999999;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;

        const cropperContent = document.createElement('div');
        cropperContent.style.cssText = `
            background: white;
            border-radius: 12px;
            padding: 20px;
            max-width: 90vw;
            max-height: 90vh;
            overflow: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            display: flex;
            flex-direction: column;
            align-items: center;
        `;

        // Create title
        const title = document.createElement('h3');
        title.textContent = `Crop Video Screenshot (${timestamp})`;
        title.style.cssText = `
            margin: 0 0 20px 0;
            color: #333;
            font-size: 18px;
            font-weight: 600;
        `;

        // Create canvas container
        const canvasContainer = document.createElement('div');
        canvasContainer.style.cssText = `
            position: relative;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
            max-width: 800px;
            max-height: 600px;
        `;

        // Create image for cropping
        const img = new Image();
        img.onload = function() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d', { willReadFrequently: true });

            // Scale image to fit container while maintaining aspect ratio
            const maxWidth = 800;
            const maxHeight = 600;
            let { width, height } = img;

            if (width > maxWidth || height > maxHeight) {
                const ratio = Math.min(maxWidth / width, maxHeight / height);
                width *= ratio;
                height *= ratio;
            }

            canvas.width = width;
            canvas.height = height;
            ctx.drawImage(img, 0, 0, width, height);

            canvasContainer.appendChild(canvas);

            // Add crop selection overlay
            addCropSelection(canvasContainer, canvas, img, timestamp, originalImageElement);
        };

        img.src = imageDataUrl;

        // Create button container
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
            display: flex;
            gap: 12px;
            justify-content: center;
        `;

        // Create buttons
        const cropButton = createCropButton('Crop & Save', 'primary');
        const cancelButton = createCropButton('Cancel', 'secondary');

        // Add event listeners
        cancelButton.addEventListener('click', () => {
            closeCropperModal(cropperModal);
        });

        // Assemble modal
        buttonContainer.appendChild(cropButton);
        buttonContainer.appendChild(cancelButton);

        cropperContent.appendChild(title);
        cropperContent.appendChild(canvasContainer);
        cropperContent.appendChild(buttonContainer);
        cropperModal.appendChild(cropperContent);

        document.body.appendChild(cropperModal);

        // Close on background click
        cropperModal.addEventListener('click', (e) => {
            if (e.target === cropperModal) {
                closeCropperModal(cropperModal);
            }
        });

        // Close on Escape key
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                closeCropperModal(cropperModal);
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);

    } catch (error) {
        console.error("Stickara: Error opening video screenshot cropper:", error);
        if (typeof showStatus === 'function') {
            showStatus(`Failed to open cropper: ${error.message}`, 'error');
        }
    }
}

/**
 * Adds crop selection functionality to the canvas.
 */
function addCropSelection(container, canvas, originalImg, timestamp, originalImageElement = null) {
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        cursor: crosshair;
    `;

    let isSelecting = false;
    let startX, startY, endX, endY;
    let selectionDiv = null;

    overlay.addEventListener('mousedown', (e) => {
        isSelecting = true;
        const rect = canvas.getBoundingClientRect();
        startX = e.clientX - rect.left;
        startY = e.clientY - rect.top;

        // Create selection rectangle
        if (selectionDiv) selectionDiv.remove();
        selectionDiv = document.createElement('div');
        selectionDiv.style.cssText = `
            position: absolute;
            border: 2px dashed #007bff;
            background: rgba(0, 123, 255, 0.1);
            pointer-events: none;
        `;
        overlay.appendChild(selectionDiv);
    });

    overlay.addEventListener('mousemove', (e) => {
        if (!isSelecting) return;

        const rect = canvas.getBoundingClientRect();
        endX = e.clientX - rect.left;
        endY = e.clientY - rect.top;

        const left = Math.min(startX, endX);
        const top = Math.min(startY, endY);
        const width = Math.abs(endX - startX);
        const height = Math.abs(endY - startY);

        selectionDiv.style.left = left + 'px';
        selectionDiv.style.top = top + 'px';
        selectionDiv.style.width = width + 'px';
        selectionDiv.style.height = height + 'px';
    });

    overlay.addEventListener('mouseup', () => {
        isSelecting = false;

        // Update crop button to handle the selection
        const cropButton = document.querySelector('#Stickara-video-screenshot-cropper .crop-primary');
        if (cropButton && selectionDiv) {
            cropButton.onclick = () => {
                performCrop(canvas, originalImg, startX, startY, endX, endY, timestamp, originalImageElement);
            };
        }
    });

    container.appendChild(overlay);
}

/**
 * Performs the actual crop operation with in-place editing support.
 */
function performCrop(displayCanvas, originalImg, startX, startY, endX, endY, timestamp, originalImageElement = null) {
    try {
        const left = Math.min(startX, endX);
        const top = Math.min(startY, endY);
        const width = Math.abs(endX - startX);
        const height = Math.abs(endY - startY);

        if (width < 10 || height < 10) {
            alert('Please select a larger area to crop.');
            return;
        }

        // Calculate scaling factors
        const scaleX = originalImg.width / displayCanvas.width;
        const scaleY = originalImg.height / displayCanvas.height;

        // Create crop canvas with performance optimization
        const cropCanvas = document.createElement('canvas');
        const cropCtx = cropCanvas.getContext('2d', { willReadFrequently: true });

        cropCanvas.width = width * scaleX;
        cropCanvas.height = height * scaleY;

        // Draw cropped portion
        cropCtx.drawImage(
            originalImg,
            left * scaleX, top * scaleY, width * scaleX, height * scaleY,
            0, 0, cropCanvas.width, cropCanvas.height
        );

        // Convert to data URL
        const croppedDataUrl = cropCanvas.toDataURL('image/png', 1.0);

        // Close cropper first
        const cropperModal = document.getElementById('Stickara-video-screenshot-cropper');
        if (cropperModal) {
            closeCropperModal(cropperModal);
        }

        if (originalImageElement) {
            // In-place editing: update the original image
            updateImageInPlace(originalImageElement, croppedDataUrl, timestamp, 'Cropped');
        } else {
            // Regular crop: download the cropped image
            downloadVideoScreenshot(croppedDataUrl, `${timestamp}-cropped`);
        }

        if (typeof showStatus === 'function') {
            const action = originalImageElement ? 'updated in-place' : 'saved';
            showStatus(`Cropped screenshot ${action} for ${timestamp}`, 'success');
        }

    } catch (error) {
        console.error("Stickara: Error performing crop:", error);
        if (typeof showStatus === 'function') {
            showStatus('Failed to crop image', 'error');
        }
    }
}

/**
 * Updates an image element in-place with new image data.
 * @param {HTMLImageElement} imageElement - The image element to update
 * @param {string} newImageDataUrl - The new image data URL
 * @param {string} timestamp - The timestamp
 * @param {string} action - The action performed (e.g., 'Edited', 'Cropped')
 */
function updateImageInPlace(imageElement, newImageDataUrl, timestamp, action) {
    try {
        // Preserve original attributes
        const originalTimestamp = imageElement.getAttribute('data-timestamp');
        const originalId = imageElement.id;
        const originalClass = imageElement.className;
        const originalStyle = imageElement.getAttribute('style');

        // Update the image source with the new version
        imageElement.src = newImageDataUrl;
        imageElement.setAttribute('data-image-data', newImageDataUrl);

        // Update the title to indicate the action performed
        imageElement.title = `Video Screenshot at ${timestamp} - Click for options (${action})`;

        // Preserve all original styling and functionality
        imageElement.id = originalId;
        imageElement.className = originalClass;
        imageElement.setAttribute('style', originalStyle);
        imageElement.setAttribute('data-timestamp', originalTimestamp);

        // Ensure click functionality is maintained
        if (!imageElement.hasAttribute('data-click-handler-attached')) {
            imageElement.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                showVideoScreenshotClickOptions(imageElement);
            }, { passive: false });
            imageElement.setAttribute('data-click-handler-attached', 'true');
        }

        // Trigger save if available
        if (typeof scheduleSave === 'function') {
            requestIdleCallback(() => {
                scheduleSave();
            });
        }

        console.log(`Stickara: Successfully updated video screenshot in-place for ${timestamp} (${action})`);

    } catch (error) {
        console.error("Stickara: Error updating image in-place:", error);
        throw error;
    }
}

/**
 * Creates a styled button for the cropper.
 */
function createCropButton(text, type) {
    const button = document.createElement('button');
    button.textContent = text;
    button.className = `crop-${type}`;

    const isPrimary = type === 'primary';
    button.style.cssText = `
        background: ${isPrimary ? '#007bff' : '#6c757d'};
        color: white;
        border: none;
        border-radius: 6px;
        padding: 10px 20px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;
        min-width: 100px;
    `;

    button.addEventListener('mouseenter', () => {
        button.style.backgroundColor = isPrimary ? '#0056b3' : '#545b62';
    });

    button.addEventListener('mouseleave', () => {
        button.style.backgroundColor = isPrimary ? '#007bff' : '#6c757d';
    });

    return button;
}

/**
 * Closes the cropper modal.
 */
function closeCropperModal(modal) {
    if (modal && modal.parentNode) {
        modal.parentNode.removeChild(modal);
    }
}

// Performance tracking for video screenshots
let videoScreenshotTracker = {
    count: 0,
    totalSize: 0,
    screenshots: new Map()
};

/**
 * Tracks video screenshot performance and memory usage.
 * @param {string} uniqueId - The unique ID of the screenshot
 * @param {number} sizeKB - Size of the screenshot in KB
 */
function trackVideoScreenshot(uniqueId, sizeKB) {
    videoScreenshotTracker.count++;
    videoScreenshotTracker.totalSize += sizeKB;
    videoScreenshotTracker.screenshots.set(uniqueId, {
        size: sizeKB,
        timestamp: Date.now()
    });

    console.log(`Stickara: Video screenshot tracked - Count: ${videoScreenshotTracker.count}, Total: ${Math.round(videoScreenshotTracker.totalSize)}KB`);

    // Check if we need to optimize performance
    if (videoScreenshotTracker.count > 10 || videoScreenshotTracker.totalSize > 50000) { // 50MB threshold
        optimizeVideoScreenshotPerformance();
    }
}

/**
 * Advanced performance optimization for large notes with many screenshots.
 * Implements virtual scrolling and intelligent DOM management.
 */
function optimizeVideoScreenshotPerformance() {
    console.log("Stickara: Starting advanced performance optimization...");

    try {
        const screenshots = document.querySelectorAll('.Stickara-video-screenshot');
        const noteContainer = findNoteContainer();

        if (!noteContainer) {
            console.warn("Stickara: Note container not found for optimization");
            return;
        }

        console.log(`Stickara: Found ${screenshots.length} screenshots to optimize`);

        // Implement virtual scrolling for large note content
        if (screenshots.length > 15) {
            implementVirtualScrolling(noteContainer, screenshots);
        }

        // Optimize visible screenshots with intersection observer
        implementIntersectionObserver(screenshots);

        // Batch DOM operations for better performance
        batchDOMOptimizations(screenshots);

        // Clean up old canvas references and memory
        cleanupCanvasReferences();

        // Implement smart memory management
        implementSmartMemoryManagement();

        console.log("Stickara: Advanced performance optimization completed");

    } catch (error) {
        console.error("Stickara: Error in advanced performance optimization:", error);
    }
}

/**
 * Finds the main note container for optimization.
 */
function findNoteContainer() {
    // Look for common note container patterns
    const selectors = [
        '[contenteditable="true"]',
        '.note-content',
        '.Stickara-note',
        '#note-content',
        '.editor-content'
    ];

    for (const selector of selectors) {
        const container = document.querySelector(selector);
        if (container && container.contains(document.querySelector('.Stickara-video-screenshot'))) {
            return container;
        }
    }

    // Fallback: find parent container of screenshots
    const firstScreenshot = document.querySelector('.Stickara-video-screenshot');
    if (firstScreenshot) {
        let parent = firstScreenshot.parentElement;
        while (parent && parent !== document.body) {
            if (parent.scrollHeight > parent.clientHeight ||
                parent.style.overflow === 'auto' ||
                parent.style.overflow === 'scroll') {
                return parent;
            }
            parent = parent.parentElement;
        }
        return firstScreenshot.parentElement;
    }

    return null;
}

/**
 * Implements virtual scrolling for large note content.
 */
function implementVirtualScrolling(container, screenshots) {
    console.log("Stickara: Implementing virtual scrolling for large note");

    // Create virtual scroll manager
    if (!container._stickaraVirtualScroll) {
        container._stickaraVirtualScroll = {
            visibleRange: { start: 0, end: 10 },
            itemHeight: 300, // Estimated screenshot height
            buffer: 5, // Number of items to keep as buffer
            lastScrollTop: 0,
            throttleTimeout: null
        };

        // Add scroll listener with throttling
        container.addEventListener('scroll', () => {
            const vs = container._stickaraVirtualScroll;

            clearTimeout(vs.throttleTimeout);
            vs.throttleTimeout = setTimeout(() => {
                updateVirtualScrollView(container, screenshots);
            }, 16); // ~60fps
        }, { passive: true });

        // Initial setup
        updateVirtualScrollView(container, screenshots);
    }
}

/**
 * Updates the virtual scroll view to show only visible items.
 */
function updateVirtualScrollView(container, screenshots) {
    const vs = container._stickaraVirtualScroll;
    const scrollTop = container.scrollTop;
    const containerHeight = container.clientHeight;

    // Calculate visible range
    const startIndex = Math.max(0, Math.floor(scrollTop / vs.itemHeight) - vs.buffer);
    const endIndex = Math.min(screenshots.length - 1,
        Math.ceil((scrollTop + containerHeight) / vs.itemHeight) + vs.buffer);

    // Only update if range changed significantly
    if (Math.abs(startIndex - vs.visibleRange.start) > 2 ||
        Math.abs(endIndex - vs.visibleRange.end) > 2) {

        vs.visibleRange = { start: startIndex, end: endIndex };

        // Hide screenshots outside visible range
        screenshots.forEach((img, index) => {
            if (index < startIndex || index > endIndex) {
                if (img.style.display !== 'none') {
                    img.style.display = 'none';
                    img._stickaraHidden = true;
                }
            } else {
                if (img._stickaraHidden) {
                    img.style.display = 'block';
                    img._stickaraHidden = false;
                }
            }
        });

        console.log(`Stickara: Virtual scroll updated - showing items ${startIndex} to ${endIndex}`);
    }
}

/**
 * Implements intersection observer for intelligent loading.
 */
function implementIntersectionObserver(screenshots) {
    if (!window.IntersectionObserver) return;

    // Create intersection observer for screenshots
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            const img = entry.target;

            if (entry.isIntersecting) {
                // Load full quality when visible
                if (img.dataset.originalSrc && img.dataset.lazyLoaded === 'true') {
                    requestIdleCallback(() => {
                        img.src = img.dataset.originalSrc;
                        img.style.filter = 'none';
                        img.removeAttribute('data-lazy-loaded');
                    });
                }
            } else {
                // Optionally convert to thumbnail when not visible
                if (!img.dataset.lazyLoaded && img.src.length > 100000) { // Large image
                    requestIdleCallback(() => {
                        createLazyLoadedScreenshot(img);
                    });
                }
            }
        });
    }, {
        rootMargin: '100px', // Start loading 100px before entering viewport
        threshold: 0.1
    });

    screenshots.forEach(img => observer.observe(img));
}

/**
 * Batches DOM optimizations for better performance.
 */
function batchDOMOptimizations(screenshots) {
    // Use document fragment for batch operations
    requestIdleCallback(() => {
        const optimizations = [];

        screenshots.forEach((img, index) => {
            // Defer non-critical style updates
            if (index > 10) { // Only optimize screenshots beyond the first 10
                optimizations.push(() => {
                    // Add will-change for better compositing
                    img.style.willChange = 'transform';

                    // Use transform3d for hardware acceleration
                    if (!img.style.transform.includes('translate3d')) {
                        img.style.transform += ' translate3d(0,0,0)';
                    }
                });
            }
        });

        // Execute optimizations in batches
        const batchSize = 5;
        let currentBatch = 0;

        function processBatch() {
            const start = currentBatch * batchSize;
            const end = Math.min(start + batchSize, optimizations.length);

            for (let i = start; i < end; i++) {
                optimizations[i]();
            }

            currentBatch++;
            if (currentBatch * batchSize < optimizations.length) {
                requestIdleCallback(processBatch);
            }
        }

        processBatch();
    });
}

/**
 * Implements smart memory management for large notes.
 */
function implementSmartMemoryManagement() {
    // Monitor memory usage
    if (performance.memory) {
        const memoryInfo = performance.memory;
        const usedMB = Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024);
        const limitMB = Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024);
        const usagePercent = (usedMB / limitMB) * 100;

        console.log(`Stickara: Memory usage: ${usedMB}MB / ${limitMB}MB (${usagePercent.toFixed(1)}%)`);

        // If memory usage is high, trigger aggressive optimization
        if (usagePercent > 70) {
            // Convert more images to thumbnails
            const largeImages = document.querySelectorAll('.Stickara-video-screenshot:not([data-lazy-loaded])');
            largeImages.forEach((img, index) => {
                if (index > 5) { // Keep first 5 at full quality
                    requestIdleCallback(() => {
                        createLazyLoadedScreenshot(img);
                    });
                }
            });

            // Force garbage collection if available
            if (window.gc) {
                setTimeout(() => window.gc(), 1000);
            }
        }
    }

    // Set up periodic memory cleanup
    if (!window._stickaraMemoryCleanup) {
        window._stickaraMemoryCleanup = setInterval(() => {
            cleanupCanvasReferences();

            // Clean up unused image data
            const hiddenImages = document.querySelectorAll('.Stickara-video-screenshot[style*="display: none"]');
            hiddenImages.forEach(img => {
                if (img.src.length > 50000 && !img.dataset.originalSrc) { // Large image without backup
                    img.dataset.originalSrc = img.src;
                    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+';
                }
            });
        }, 30000); // Every 30 seconds
    }
}

/**
 * Converts a large screenshot to a lazy-loaded placeholder.
 * @param {HTMLImageElement} img - The image element to optimize
 */
function createLazyLoadedScreenshot(img) {
    const timestamp = img.getAttribute('data-timestamp');
    const originalSrc = img.src;
    const sizeKB = Math.round((originalSrc.length * 0.75) / 1024);

    // Create a small thumbnail
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d', { willReadFrequently: true });

    const tempImg = new Image();
    tempImg.onload = function() {
        // Create a small thumbnail (200x150 max)
        const maxWidth = 200;
        const maxHeight = 150;
        let { width, height } = tempImg;

        if (width > maxWidth || height > maxHeight) {
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width *= ratio;
            height *= ratio;
        }

        canvas.width = width;
        canvas.height = height;
        ctx.drawImage(tempImg, 0, 0, width, height);

        const thumbnailSrc = canvas.toDataURL('image/jpeg', 0.7);

        // Replace the original image with thumbnail
        img.src = thumbnailSrc;
        img.setAttribute('data-original-src', originalSrc);
        img.setAttribute('data-lazy-loaded', 'true');
        img.style.filter = 'brightness(0.9)';
        img.title = `Video Screenshot (${timestamp}) - ${sizeKB}KB - Click to load full quality`;

        // Add click handler to load full quality
        const loadFullQuality = () => {
            img.src = originalSrc;
            img.style.filter = 'none';
            img.title = `Video Screenshot at ${timestamp} - Click for options`;
            img.removeEventListener('click', loadFullQuality);
        };

        img.addEventListener('click', loadFullQuality, { once: true });
    };

    tempImg.src = originalSrc;
}

/**
 * Cleans up canvas references to prevent memory leaks.
 */
function cleanupCanvasReferences() {
    // Remove any temporary canvases that might be lingering
    const tempCanvases = document.querySelectorAll('canvas[id*="temp"], canvas[id*="screenshot-temp"]');
    tempCanvases.forEach(canvas => {
        if (canvas.parentNode) {
            canvas.parentNode.removeChild(canvas);
        }
    });

    // Clear any cached image data that's no longer needed
    const oldScreenshots = Array.from(videoScreenshotTracker.screenshots.entries())
        .filter(([id, data]) => Date.now() - data.timestamp > 300000) // 5 minutes old
        .map(([id]) => id);

    oldScreenshots.forEach(id => {
        videoScreenshotTracker.screenshots.delete(id);
        videoScreenshotTracker.totalSize -= videoScreenshotTracker.screenshots.get(id)?.size || 0;
    });
}

/**
 * Monitors note performance and triggers optimizations if needed.
 */
function monitorNotePerformance() {
    try {
        const startTime = performance.now();

        // Check DOM complexity
        const screenshots = document.querySelectorAll('.Stickara-video-screenshot');
        const noteElements = document.querySelectorAll('[contenteditable="true"], .note-content');

        let totalElements = 0;
        let totalImageSize = 0;

        noteElements.forEach(note => {
            totalElements += note.querySelectorAll('*').length;
        });

        screenshots.forEach(img => {
            totalImageSize += img.src.length;
        });

        const complexityScore = (totalElements * 0.1) + (screenshots.length * 2) + (totalImageSize / 1000000);

        console.log(`Stickara: Note performance metrics:
            - Screenshots: ${screenshots.length}
            - DOM elements: ${totalElements}
            - Image data size: ${Math.round(totalImageSize / 1024 / 1024)}MB
            - Complexity score: ${complexityScore.toFixed(2)}`);

        // Trigger optimization if complexity is high
        if (complexityScore > 50) {
            console.log("Stickara: High complexity detected, triggering performance optimization");

            if (typeof showStatus === 'function') {
                showStatus('Optimizing note performance...', 'info');
            }

            // Defer optimization to prevent blocking
            requestIdleCallback(() => {
                optimizeVideoScreenshotPerformance();
            }, { timeout: 5000 });
        }

        // Monitor frame rate
        monitorFrameRate();

        const endTime = performance.now();
        console.log(`Stickara: Performance monitoring completed in ${(endTime - startTime).toFixed(2)}ms`);

    } catch (error) {
        console.error("Stickara: Error monitoring note performance:", error);
    }
}

/**
 * Monitors frame rate to detect performance issues.
 */
function monitorFrameRate() {
    if (!window._stickaraFrameMonitor) {
        window._stickaraFrameMonitor = {
            frames: [],
            monitoring: false
        };
    }

    const monitor = window._stickaraFrameMonitor;

    if (monitor.monitoring) return; // Already monitoring

    monitor.monitoring = true;
    monitor.frames = [];

    let frameCount = 0;
    const maxFrames = 60; // Monitor for 1 second at 60fps

    function measureFrame() {
        const now = performance.now();
        monitor.frames.push(now);
        frameCount++;

        if (frameCount < maxFrames) {
            requestAnimationFrame(measureFrame);
        } else {
            // Calculate average frame time
            const totalTime = monitor.frames[monitor.frames.length - 1] - monitor.frames[0];
            const avgFrameTime = totalTime / (monitor.frames.length - 1);
            const fps = 1000 / avgFrameTime;

            console.log(`Stickara: Average FPS: ${fps.toFixed(1)}`);

            // If FPS is low, trigger optimization
            if (fps < 30) {
                console.log("Stickara: Low FPS detected, triggering performance optimization");
                requestIdleCallback(() => {
                    optimizeVideoScreenshotPerformance();
                });
            }

            monitor.monitoring = false;
        }
    }

    requestAnimationFrame(measureFrame);
}

/**
 * Logs storage usage information for monitoring purposes.
 */
async function logStorageUsage() {
    try {
        // Check Chrome storage usage with unlimited storage permission
        const storageInfo = await new Promise((resolve) => {
            chrome.storage.local.getBytesInUse(null, (bytesInUse) => {
                resolve({ bytesInUse, error: chrome.runtime.lastError });
            });
        });

        if (storageInfo.error) {
            console.warn("Stickara: Could not check storage usage:", storageInfo.error);
            return;
        }

        const bytesInUse = storageInfo.bytesInUse;
        const usageMB = Math.round(bytesInUse / (1024 * 1024) * 100) / 100; // Convert to MB with 2 decimal places

        console.log(`Stickara: Storage usage: ${usageMB}MB (unlimited storage enabled)`);

        if (typeof showStatus === 'function' && usageMB > 100) {
            showStatus(`Storage usage: ${usageMB}MB (high-quality mode)`, 'info');
        }

    } catch (error) {
        console.warn("Stickara: Error checking storage usage:", error);
    }
}

console.log("Stickara: Screenshot Logic Loaded (v3.1 - Complete File)");
// --- END OF FILE content-screenshot.js ---