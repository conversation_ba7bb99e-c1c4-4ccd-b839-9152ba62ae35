// --- START OF FILE content-utils.js ---

/**
 * Safely escapes HTML special characters to prevent XSS attacks
 * @param {string} text - The text to escape
 * @returns {string} - The escaped text safe for insertion into HTML
 */
function escapeHtml(text) {
    if (!text) return '';
    return String(text)
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

/**
 * Safely sanitizes HTML content to prevent XSS attacks while preserving <mark> tags for highlighting
 * @param {string} html - The HTML content to sanitize
 * @returns {string} - The sanitized HTML with only safe tags preserved
 */
function sanitizeHtmlPreserveMarks(html) {
    if (!html) return '';

    // Create a temporary div to extract plain text
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Get the text content (removes all HTML tags)
    const plainText = tempDiv.textContent || tempDiv.innerText || '';

    // Then escape all HTML characters to prevent any script execution
    const escapedText = escapeHtml(plainText);

    return escapedText;
}

/**
 * Safely highlights search terms in text while preventing XSS attacks
 * @param {string} text - The plain text to highlight
 * @param {string} query - The search query to highlight
 * @returns {string} - The HTML with safely highlighted search terms
 */
function safeHighlightSearchTerms(text, query) {
    if (!text || !query) return escapeHtml(text || '');

    // First ensure the text is plain text with no HTML
    const plainText = typeof text === 'string' ? text : String(text || '');

    // Escape the plain text to prevent any HTML injection
    const escapedText = escapeHtml(plainText);

    try {
        // Escape regex special characters in the query
        const escapedQuery = query.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
        if (!escapedQuery) return escapedText;

        // Create a regex to find the query terms
        const regex = new RegExp(escapedQuery, 'gi');

        // Replace matches with <mark> tags
        return escapedText.replace(regex, '<mark>$&</mark>');
    } catch (e) {
        console.warn("Regex error during highlighting:", e);
        return escapedText; // Return safely escaped text if regex fails
    }
}

// Make the functions available globally
window.escapeHtml = escapeHtml;
window.sanitizeHtmlPreserveMarks = sanitizeHtmlPreserveMarks;
window.safeHighlightSearchTerms = safeHighlightSearchTerms;

/**
 * Extracts plain text from HTML content
 * @param {string} html - The HTML content to convert to plain text
 * @returns {string} - The plain text without HTML tags
 */
window.getPlainText = function(html) {
    if (!html) return '';

    // Create a temporary div element
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Get the text content
    return tempDiv.textContent || tempDiv.innerText || '';
}

/**
 * Formats a timestamp into a relative time string (e.g., "5 min ago").
 * @param {number | null} timestamp - The timestamp in milliseconds, or null.
 * @returns {string} The formatted relative time string or empty string.
 */
function formatRelativeTime(timestamp) {
    if (!timestamp) return '';
    const now = Date.now();
    const diff = now - timestamp;
    const seconds = Math.floor(diff / 1000);

    if (seconds < 5) return 'just now';
    if (seconds < 60) return `${seconds} sec ago`;

    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes} min ago`;

    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours} hr ago`;

    const days = Math.floor(hours / 24);
    if (days < 7) return `${days} day${days !== 1 ? 's' : ''} ago`;

    try {
        const date = new Date(timestamp);
        return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
    } catch (e) {
        console.warn("Stickara: Error formatting date", e);
        return ''; // Fallback for invalid date
    }
}

/**
 * Inserts HTML content at the current cursor position within noteText.
 * Added try/catch and more logging. Ensures save is scheduled.
 * @param {string} html - The HTML string to insert.
 */
function insertHtmlAtCursor(html) {
    if (!noteText) {
        console.error("Stickara: insertHtmlAtCursor - noteText not found.");
        return;
    }
    noteText.focus(); // Ensure the contentEditable div has focus
    try {
        // Use modern approach instead of deprecated execCommand
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            range.deleteContents();
            const fragment = range.createContextualFragment(html);
            range.insertNode(fragment);
            range.collapse(false);
            selection.removeAllRanges();
            selection.addRange(range);
        } else {
            // Fallback: append to end of content
            const fragment = document.createRange().createContextualFragment(html);
            noteText.appendChild(fragment);
        }

        // Enhance any tables that were just inserted - Multiple attempts for reliability
        setTimeout(() => {
            if (typeof enhanceAllTablesInNote === 'function') {
                enhanceAllTablesInNote();
                console.log('Stickara: HTML insertion tables enhanced (first attempt)');
            }
        }, 50);

        setTimeout(() => {
            if (typeof enhanceAllTablesInNote === 'function') {
                enhanceAllTablesInNote();
                console.log('Stickara: HTML insertion tables enhanced (second attempt)');
            }
        }, 150);

    } catch (e) {
        console.error("Stickara: CRITICAL Error using execCommand('insertHTML'):", e);
        alert("Stickara: Failed to insert content. The editor might be in an unexpected state.");
    } finally {
        scheduleSave(); // Save after insertion attempt
    }
}

/**
 * Inserts a DOM Node at the current cursor position within noteText.
 * Handles cases where the selection is outside or non-existent.
 * Added try/catch and logging. Ensures save is scheduled.
 * @param {Node} node - The DOM Node to insert.
 */
function insertNodeAtCursor(node) {
    if (!noteText) {
        console.error("Stickara: insertNodeAtCursor - noteText not found.");
        return;
    }
    noteText.focus();
    const sel = window.getSelection();

    try {
        if (sel && sel.rangeCount > 0) {
            const range = sel.getRangeAt(0);
            if (!noteText.contains(range.commonAncestorContainer)) {
                noteText.appendChild(node);
                 range.selectNodeContents(noteText);
                 range.collapse(false);
                 sel.removeAllRanges();
                 sel.addRange(range);
            } else {
                range.deleteContents();
                range.insertNode(node);
                range.setStartAfter(node);
                range.collapse(true);
                sel.removeAllRanges();
                sel.addRange(range);
            }
        } else {
            noteText.appendChild(node);
            const newRange = document.createRange();
            newRange.selectNodeContents(noteText);
            newRange.collapse(false);
            if (sel) {
                sel.removeAllRanges();
                sel.addRange(newRange);
            }
        }
    } catch (e) {
        console.error("Stickara: CRITICAL Error inserting node or manipulating selection:", e);
        alert("Stickara: Failed to insert content correctly.");
    } finally {
        scheduleSave();
    }
}

/**
 * Gets the currently selected text from the page, if any.
 * Checks if the selection is within the note text area.
 * @returns {string} The selected text, or an empty string.
 */
function getSelectedText() {
    const selection = window.getSelection();
    if (selection && !selection.isCollapsed && selection.rangeCount > 0) {
        if (noteText && noteText.contains(selection.anchorNode)) {
            // console.log("Stickara: Selection is inside noteText, ignoring for {SELECTION} placeholder.");
            return ''; // Don't grab text from inside the note itself
        }
        return selection.toString();
    }
    return '';
}

/**
 * Shows temporary feedback on a tool button.
 * @param {string} message - The message to display (e.g., "✅ Copied!").
 * @param {boolean} isError - True if the message indicates an error.
 * @param {string} buttonAriaLabel - The aria-label of the target button.
 */
function showCopyFeedback(message, isError = false, buttonAriaLabel) {
    if (!noteContainer) return;
    const button = noteContainer.querySelector(`.Stickara-tool-btn[aria-label="${buttonAriaLabel}"]`);
    if (!button) {
        console.warn(`showCopyFeedback: Button with aria-label "${buttonAriaLabel}" not found.`);
        return;
    }

    const originalHtml = button.innerHTML;
    const feedbackIcon = isError ? '❌' : '✅';
    const feedbackText = message.replace(/[✅❌]/, '').trim();
    let feedbackHtml = `<span class="Stickara-icon">${feedbackIcon}</span>`;
    if (button.querySelector('.Stickara-text')) { // If original button had text
         feedbackHtml += `<span class="Stickara-text">${feedbackText}</span>`;
    } else { // If original was icon-only
        feedbackHtml += feedbackText;
    }

    button.innerHTML = feedbackHtml;
    button.style.color = isError ? 'var(--Stickara-error)' : 'var(--Stickara-success)';
    button.disabled = true;

    if (button.feedbackTimeout) clearTimeout(button.feedbackTimeout);

    button.feedbackTimeout = setTimeout(() => {
        if (button) {
            button.innerHTML = originalHtml;
            button.style.color = '';
            button.disabled = false;
            delete button.feedbackTimeout;
        }
    }, 1500);
}

/**
 * Shows a temporary "Saved!" feedback flash on the note header's title input placeholder.
 * @param {number} currentNoteIndex - The index of the note being saved (1-based).
 */
function showSaveFeedback(currentNoteIndex) {
    if (!noteHeader || !timestampSpan || !noteTitleInput) return;

    if (noteTitleInput.dataset.feedbackActive === 'true') return;

    const currentTitle = noteTitleInput.value.trim();
    const defaultPlaceholder = `Note ${currentNoteIndex}`;

    if (!currentTitle || noteTitleInput.placeholder === currentTitle) {
        noteTitleInput.dataset.feedbackActive = 'true';
        noteTitleInput.placeholder = '✅ Saved!';
        noteTitleInput.style.fontWeight = 'bold';
        noteTitleInput.style.color = 'var(--Stickara-success)';
        noteHeader.style.transition = 'background-color 0.1s ease-out, box-shadow 0.2s ease-out';
        noteHeader.style.backgroundColor = 'var(--Stickara-save-flash)';
        noteHeader.style.boxShadow = 'inset 0 0 8px rgba(53, 230, 62, 0.4)';

        if (noteTitleInput.feedbackTimeout) clearTimeout(noteTitleInput.feedbackTimeout);

        noteTitleInput.feedbackTimeout = setTimeout(() => {
            if (noteTitleInput) {
                if (noteTitleInput.placeholder === '✅ Saved!') {
                    noteTitleInput.placeholder = defaultPlaceholder; // Restore default placeholder
                }
                noteTitleInput.style.fontWeight = '';
                noteTitleInput.style.color = '';
                delete noteTitleInput.dataset.feedbackActive;
                delete noteTitleInput.feedbackTimeout;
            }
            if (noteHeader) {
                 noteHeader.style.backgroundColor = '';
                 noteHeader.style.boxShadow = '';
            }
        }, 1200);
    }
    timestampSpan.innerText = `Saved: ${formatRelativeTime(Date.now())}`;
}

/**
 * Checks if a canvas is effectively empty (all white or transparent pixels).
 * @param {HTMLCanvasElement} canvas - The canvas element to check.
 * @returns {boolean} True if the canvas is considered empty, false otherwise.
 */
function isCanvasEmpty(canvas) {
    if (!canvas) return true;
    try {
        const ctx = canvas.getContext('2d', { willReadFrequently: true });
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const pixelBuffer = new Uint32Array(imageData.data.buffer);
        return !pixelBuffer.some(color => (color & 0xFFFFFFFF) !== 0xFFFFFFFF && (color & 0xFF000000) !== 0);
    } catch (e) {
        console.error("Stickara: Error checking canvas emptiness:", e);
        return false;
    }
}

/**
 * Converts HTML content to a basic, standalone HTML document string.
 * @param {string} htmlContent - The innerHTML from the note editor.
 * @param {string} [pageTitle='Stickara Note'] - Optional title for the HTML document.
 * @returns {string} A full HTML document string.
 */
function convertToBasicHTML(htmlContent, pageTitle = 'Stickara Note') {
    const basicCSS = `
        body { font-family: sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: auto; }
        h1, h2, h3 { margin-top: 1.5em; margin-bottom: 0.5em; }
        blockquote { border-left: 4px solid #ccc; padding-left: 1em; margin-left: 0; color: #555; font-style: italic; }
        pre { background-color: #f4f4f4; border: 1px solid #ddd; padding: 10px; border-radius: 4px; overflow-x: auto; white-space: pre-wrap; word-wrap: break-word; }
        code { font-family: monospace; }
        mark { background-color: yellow; padding: 0.1em 0.2em; }
        img { max-width: 100%; height: auto; display: block; margin: 10px 0; border-radius: 4px; border: 1px solid #eee; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 1em; }
        th, td { border: 1px solid #ccc; padding: 8px; text-align: left; vertical-align: top; }
        th { background-color: #f2f2f2; }
        ul, ol { margin-left: 1.5em; padding-left: 0.5em; }
        li { margin-bottom: 0.3em; }
        .Stickara-equation { border: 1px dashed #ccc; padding: 2px 5px; display: inline-block; background-color: #f9f9f9; }
        .Stickara-checklist { list-style: none; padding-left: 0.5em; }
        .Stickara-checklist-item { list-style: none; margin-bottom: 0.3em; padding-left: 1.8em; position: relative; }
        .Stickara-checklist-item::before { content: '☐'; position: absolute; left: 0; top: 0.05em; font-size: 1.2em; line-height: 1; color: #777; }
        .Stickara-checklist-item.checked::before { content: '☑'; color: #34D399; font-weight: bold; }
        .Stickara-checklist-item.checked { color: #888; text-decoration: line-through; }
        .Stickara-timestamp-link { color: #0a58ca; border-bottom: 1px dotted #0a58ca; }
        .Stickara-video-associated-url a { color: #0a58ca; border-bottom: 1px dotted #0a58ca; }
    `;
    const escapedTitle = escapeHtml(pageTitle);

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${escapedTitle}</title>
    <style>${basicCSS}</style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;
}


/**
 * Converts HTML content from the note editor to Markdown.
 * Uses DOMParser and tree walking for better accuracy than regex alone.
 * @param {string} htmlContent - The innerHTML from the note editor.
 * @returns {string} The Markdown representation.
 */
function convertToMarkdown(htmlContent) {
    try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(`<div>${htmlContent}</div>`, 'text/html');
        const body = doc.body.firstChild;

        let markdown = '';

        function processNode(node, listLevel = 0, listType = null) {
            let prefix = '';
            let suffix = '';

            switch (node.nodeName) {
                case '#text':
                    let textContent = node.textContent.replace(/[ \t\r\n]+/g, ' ');
                    if (textContent.trim()) markdown += textContent;
                    suffix = '';
                    break;
                case 'BR':
                    if (!markdown.endsWith('\n\n') && !markdown.endsWith('\n- ') && !markdown.endsWith('\n1. ') && markdown.length > 0) {
                         markdown += '  \n';
                     }
                    suffix = '';
                    break;
                case 'H1': prefix = '# '; suffix = '\n\n'; break;
                case 'H2': prefix = '## '; suffix = '\n\n'; break;
                case 'H3': prefix = '### '; suffix = '\n\n'; break;
                case 'H4': prefix = '#### '; suffix = '\n\n'; break;
                case 'H5': prefix = '##### '; suffix = '\n\n'; break;
                case 'H6': prefix = '###### '; suffix = '\n\n'; break;
                case 'P': prefix = ''; suffix = '\n\n'; break;
                case 'BLOCKQUOTE':
                    let blockquoteContent = '';
                    node.childNodes.forEach(child => blockquoteContent += processNode(child, listLevel, listType));
                    markdown += blockquoteContent.trim().split('\n').map(line => '> ' + line).join('\n') + '\n\n';
                    return '';
                case 'UL':
                case 'OL':
                    suffix = '\n';
                    node.childNodes.forEach((child) => {
                        if (child.nodeName === 'LI') {
                            processNode(child, listLevel + 1, node.nodeName);
                        } else if (child.nodeType === Node.TEXT_NODE && child.textContent.trim()) {
                             markdown += '  '.repeat(listLevel) + child.textContent.trim() + '\n';
                        }
                    });
                    return '';
                case 'LI':
                    const indent = '  '.repeat(listLevel > 0 ? listLevel - 1 : 0);
                    const marker = (listType === 'OL')
                        ? `${(node.parentElement.start || 1) + Array.from(node.parentElement.children).filter(el => el.tagName === 'LI').indexOf(node)}. `
                        : '- ';
                    if (node.classList.contains('Stickara-checklist-item')) {
                         const checkMarker = node.classList.contains('checked') ? '[x] ' : '[ ] ';
                         markdown += indent + marker.replace(/[\-\*1-9]+\.\s/, '') + checkMarker;
                    } else {
                         markdown += indent + marker;
                    }
                    suffix = '\n';
                    break;
                case 'PRE':
                    const codeChild = node.querySelector('code');
                    const codeContent = codeChild ? codeChild.textContent : node.textContent;
                    const langMatch = codeChild?.className.match(/language-(\w+)/);
                    const langHint = langMatch ? langMatch[1] : '';
                    markdown += `\n\`\`\`${langHint}\n${codeContent.trim()}\n\`\`\`\n\n`;
                    return '';
                case 'CODE': prefix = '`'; suffix = '`'; break;
                case 'B': case 'STRONG': prefix = '**'; suffix = '**'; break;
                case 'I': case 'EM': prefix = '*'; suffix = '*'; break;
                case 'DEL': case 'S': prefix = '~~'; suffix = '~~'; break;
                case 'U': prefix = ''; suffix = ''; break;
                case 'A':
                    const href = node.getAttribute('href') || '';
                    let linkText = '';
                    node.childNodes.forEach(child => linkText += processNode(child, listLevel, listType));
                    prefix = '['; suffix = `](${href})`;
                    markdown += prefix + linkText + suffix;
                    return '';
                case 'IMG':
                    const src = node.getAttribute('src') || '';
                    const alt = node.getAttribute('alt') || '';
                    if (src.startsWith('data:image/')) {
                        markdown += `![Embedded Image: ${alt || 'Diagram/Image'}](${alt || 'Embedded Image'})\n\n`;
                    } else {
                        markdown += `![${alt}](${src})\n\n`;
                    }
                    return '';
                case 'MARK': prefix = '=='; suffix = '=='; break;
                case 'HR': markdown += '\n---\n\n'; return '';
                 case 'TABLE':
                     markdown += convertHtmlTableToMarkdown(node) + '\n\n';
                     return '';
                case 'SPAN':
                    if (node.classList.contains('Stickara-equation')) {
                        const latex = node.getAttribute('data-latex') || '[Equation]';
                        markdown += `$\`${latex.trim()}\`$`;
                        suffix = ''; return '';
                    } else if (node.classList.contains(TIMESTAMP_LINK_CLASS)) {
                         const timeText = node.textContent || '[@time]';
                         markdown += timeText;
                         suffix = ''; return '';
                    }
                    if (node.classList.contains('Stickara-in-note-highlight')) {
                        const style = node.dataset.style || 'color';
                        if (style === 'underline') { prefix = '_'; suffix = '_'; }
                        else if (style === 'strikethrough') { prefix = '~~'; suffix = '~~'; }
                        else { prefix = '=='; suffix = '=='; }
                    } else {
                        prefix = ''; suffix = '';
                    }
                    break;
                case 'DIV':
                     suffix = '\n\n';
                     prefix = '';
                    break;
                default:
                     if (node.nodeType === Node.ELEMENT_NODE && window.getComputedStyle(node).display.includes('block')) {
                         suffix = '\n\n';
                     } else {
                         suffix = '';
                     }
                    prefix = '';
                    break;
            }

            markdown += prefix;
            node.childNodes.forEach(child => processNode(child, listLevel, listType));
            markdown += suffix;
        }

        processNode(body);

        markdown = markdown.replace(/(\n\s*){3,}/g, '\n\n').trim();

        return markdown;

    } catch (error) {
        console.error("Stickara: Error converting HTML to Markdown:", error);
        return `[Error converting note to Markdown]\n\n${htmlContent}`; // Fallback
    }
}

/**
 * Helper function to convert an HTML TABLE element to a Markdown table.
 * Handles basic tables, may struggle with complex structures (colspan, rowspan).
 * @param {HTMLTableElement} tableNode
 * @returns {string} Markdown table string.
 */
function convertHtmlTableToMarkdown(tableNode) {
    let md = '';
    const rows = Array.from(tableNode.rows);
    if (rows.length === 0) return '';

    function getCellContent(cell) {
        return cell.textContent.replace(/\n/g, ' ').replace(/\|/g, '\\|').trim();
    }

    const headerRow = tableNode.tHead ? tableNode.tHead.rows[0] : rows[0];
    if (!headerRow) return '';
    const headerCells = Array.from(headerRow.cells).map(getCellContent);
    if (headerCells.length === 0) return '';

    md += `| ${headerCells.join(' | ')} |\n`;
    md += `|${headerCells.map(() => ' --- ').join('|')}|\n`;

    const bodyRows = tableNode.tBodies.length > 0
        ? Array.from(tableNode.tBodies).flatMap(tbody => Array.from(tbody.rows))
        : (tableNode.tHead ? rows.slice(1) : rows.slice(1));

    bodyRows.forEach(row => {
        const cells = Array.from(row.cells).map(getCellContent);
        while (cells.length < headerCells.length) cells.push('');
        while (cells.length > headerCells.length) cells.pop();
        md += `| ${cells.join(' | ')} |\n`;
    });

    return md;
}


/**
 * Formats a duration in seconds into a readable timestamp string.
 * Uses MM:SS format for videos under 1 hour, HH:MM:SS for longer videos.
 * Supports optional sub-second precision.
 * @param {number} totalSeconds - The total number of seconds.
 * @param {boolean} includeSubSeconds - Whether to include sub-second precision (default: false).
 * @returns {string} The formatted time string, or '--:--' on error.
 */
function formatSecondsToMMSS(totalSeconds, includeSubSeconds = false) {
    if (isNaN(totalSeconds) || totalSeconds < 0) {
        return '--:--';
    }

    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = includeSubSeconds ? totalSeconds % 60 : Math.floor(totalSeconds % 60);

    // Format seconds with or without sub-second precision
    let formattedSeconds;
    if (includeSubSeconds) {
        formattedSeconds = seconds.toFixed(1).padStart(4, '0');
    } else {
        formattedSeconds = String(Math.floor(seconds)).padStart(2, '0');
    }

    // Use HH:MM:SS format for videos 1 hour or longer
    if (hours > 0) {
        const paddedHours = String(hours).padStart(2, '0');
        const paddedMinutes = String(minutes).padStart(2, '0');
        return `${paddedHours}:${paddedMinutes}:${formattedSeconds}`;
    } else {
        // Use MM:SS format for videos under 1 hour
        const paddedMinutes = String(minutes).padStart(2, '0');
        return `${paddedMinutes}:${formattedSeconds}`;
    }
}

/**
 * Enhanced timestamp formatting specifically for video snippets.
 * Provides additional context and improved readability.
 * @param {number} totalSeconds - The total number of seconds.
 * @param {boolean} includeSubSeconds - Whether to include sub-second precision.
 * @param {boolean} includeContext - Whether to include additional context (e.g., "at").
 * @returns {string} The formatted timestamp string.
 */
function formatVideoTimestamp(totalSeconds, includeSubSeconds = false, includeContext = false) {
    const baseTimestamp = formatSecondsToMMSS(totalSeconds, includeSubSeconds);

    if (baseTimestamp === '--:--') {
        return baseTimestamp;
    }

    if (includeContext) {
        return `at ${baseTimestamp}`;
    }

    return baseTimestamp;
}

/**
 * Processes a template string, replacing placeholders with dynamic values.
 * Now supports both basic placeholders and advanced smart template syntax.
 * Basic Placeholders:
 *  - {DATE}: Current date (YYYY-MM-DD)
 *  - {URL}: Current page URL
 *  - {SELECTION}: Current selected text on the page (outside the note editor)
 * Smart Template Syntax:
 *  - {{Prompt:question|default}}: Interactive user prompts
 *  - {{If:condition}} ... {{EndIf}}: Conditional logic
 *  - {{DueDate+7days}}: Date calculations
 * @param {string} templateContent The raw template string.
 * @returns {Promise<string>|string} The processed string with placeholders replaced.
 */
function processPlaceholders(templateContent) {
    if (typeof templateContent !== 'string') return '';

    // Check if this is a smart template (contains {{...}} syntax)
    const hasSmartSyntax = /\{\{[^}]+\}\}/.test(templateContent);

    if (hasSmartSyntax && typeof processSmartTemplate === 'function') {
        // Return a promise for smart templates
        return processSmartTemplate(templateContent);
    } else {
        return processBasicPlaceholdersSync(templateContent);
    }
}

/**
 * Synchronous processing of basic placeholders for backward compatibility
 * @param {string} templateContent The raw template string.
 * @returns {string} The processed string with basic placeholders replaced.
 */
function processBasicPlaceholdersSync(templateContent) {
    console.log("Original Template:", templateContent.substring(0, 100) + "..."); // DEBUG

    // {DATE}
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
    const day = String(today.getDate()).padStart(2, '0');
    const formattedDate = `${year}-${month}-${day}`;
    console.log("Formatted Date:", formattedDate); // DEBUG
    templateContent = templateContent.replaceAll('{DATE}', formattedDate);

    // {URL}
    const currentUrl = window.location.href;
    console.log("Current URL:", currentUrl); // DEBUG
    templateContent = templateContent.replaceAll('{URL}', currentUrl);

    // {SELECTION} - Get selection from the main document, not necessarily the note editor
    const selection = getSelectedText();
    console.log("Selected Text (from page):", selection ? `"${selection.substring(0,50)}..."` : "(empty)"); // DEBUG
    templateContent = templateContent.replaceAll('{SELECTION}', selection);

    console.log("Processed Template:", templateContent.substring(0, 100) + "..."); // DEBUG
    console.log("--- Finished Basic Placeholders ---"); // DEBUG
    return templateContent;
}

// Make processPlaceholders available globally
window.processPlaceholders = processPlaceholders;


/**
 * Element Picker functionality
 * Allows users to click on elements in the page and extract their content
 */

// Global state for element picker
let elementPickerActive = false;
let elementPickerOriginalCursor = null;
let elementPickerHighlightedElement = null;
let elementPickerOverlay = null;
let elementPickerPopup = null;

/**
 * Toggles the element picker mode on/off
 */
function toggleElementPicker() {
    if (elementPickerActive) {
        deactivateElementPicker();
    } else {
        activateElementPicker();
    }
}

/**
 * Activates the element picker mode
 */
function activateElementPicker() {
    if (elementPickerActive) return;

    // Store original cursor
    elementPickerOriginalCursor = document.body.style.cursor;

    // Set cursor to crosshair for the entire page
    document.documentElement.style.cursor = 'crosshair';
    document.body.style.cursor = 'crosshair';

    // Add a CSS class to the body to handle cursor for all elements
    document.body.classList.add('Stickara-element-picker-active');

    // Create overlay for visual feedback
    createElementPickerOverlay();

    // Add event listeners
    document.addEventListener('mousemove', handleElementPickerMouseMove);
    document.addEventListener('click', handleElementPickerClick, true); // Use capture phase to intercept clicks before they reach links
    document.addEventListener('keydown', handleElementPickerKeyDown);

    // Add event listeners to prevent default actions on links and clickable elements
    document.addEventListener('mousedown', preventDefaultForElementPicker, true);
    document.addEventListener('mouseup', preventDefaultForElementPicker, true);
    document.addEventListener('auxclick', preventDefaultForElementPicker, true); // Middle-click
    document.addEventListener('contextmenu', preventDefaultForElementPicker, true); // Right-click

    // Set active state
    elementPickerActive = true;

    // Show feedback
    showStatus('Element Picker activated. Click on any element to grab its content.', 'info');

    // Hide note UI temporarily
    if (noteContainer) {
        noteContainer.style.pointerEvents = 'none';
        noteContainer.style.opacity = '0.5';
    }
}

/**
 * Deactivates the element picker mode
 */
function deactivateElementPicker() {
    if (!elementPickerActive) return;

    // Restore original cursor
    document.documentElement.style.cursor = '';
    document.body.style.cursor = elementPickerOriginalCursor || '';

    // Remove the CSS class from the body
    document.body.classList.remove('Stickara-element-picker-active');

    // Remove overlay
    removeElementPickerOverlay();

    // Remove popup if exists
    removeElementPickerPopup();

    // Remove the element label if it exists
    const labelElement = document.getElementById('Stickara-element-picker-label');
    if (labelElement) {
        labelElement.remove();
    }

    // Remove event listeners
    document.removeEventListener('mousemove', handleElementPickerMouseMove);
    document.removeEventListener('click', handleElementPickerClick, true);
    document.removeEventListener('keydown', handleElementPickerKeyDown);

    // Remove the event listeners that prevent default actions
    document.removeEventListener('mousedown', preventDefaultForElementPicker, true);
    document.removeEventListener('mouseup', preventDefaultForElementPicker, true);
    document.removeEventListener('auxclick', preventDefaultForElementPicker, true);
    document.removeEventListener('contextmenu', preventDefaultForElementPicker, true);

    // Reset state
    elementPickerActive = false;
    elementPickerHighlightedElement = null;

    // Show feedback
    showStatus('Element Picker deactivated.', 'info');

    // Restore note UI
    if (noteContainer) {
        noteContainer.style.pointerEvents = '';
        noteContainer.style.opacity = '';
    }
}

/**
 * Creates an overlay for visual feedback during element picking
 */
function createElementPickerOverlay() {
    // Create overlay if it doesn't exist
    if (!elementPickerOverlay) {
        elementPickerOverlay = document.createElement('div');
        elementPickerOverlay.id = 'Stickara-element-picker-overlay';
        elementPickerOverlay.style.position = 'absolute'; // Changed from fixed to absolute for better positioning
        elementPickerOverlay.style.pointerEvents = 'none';
        elementPickerOverlay.style.border = '2px solid #4285f4';
        elementPickerOverlay.style.backgroundColor = 'rgba(66, 133, 244, 0.2)'; // Slightly more visible
        elementPickerOverlay.style.zIndex = '2147483646'; // Just below max
        elementPickerOverlay.style.display = 'none';
        elementPickerOverlay.style.boxShadow = '0 0 0 2px rgba(255, 255, 255, 0.5)'; // White outline for better visibility
        elementPickerOverlay.style.transition = 'all 0.1s ease-out'; // Smooth transition
        document.body.appendChild(elementPickerOverlay);
    }
}

/**
 * Removes the element picker overlay
 */
function removeElementPickerOverlay() {
    if (elementPickerOverlay) {
        elementPickerOverlay.remove();
        elementPickerOverlay = null;
    }
}

/**
 * Creates a popup menu for selecting what to extract from an element
 * @param {HTMLElement} element - The element to extract content from
 */
function createElementPickerPopup(element) {
    // Remove existing popup if any
    removeElementPickerPopup();

    // Create popup
    elementPickerPopup = document.createElement('div');
    elementPickerPopup.id = 'Stickara-element-picker-popup';
    elementPickerPopup.style.position = 'absolute';

    // Get element position for better popup placement
    const rect = element.getBoundingClientRect();

    // Position the popup near the element rather than at the mouse position
    // This makes it clearer which element is being selected
    elementPickerPopup.style.left = `${rect.left + window.scrollX + rect.width / 2}px`;
    elementPickerPopup.style.top = `${rect.bottom + window.scrollY + 10}px`; // 10px below the element
    elementPickerPopup.style.backgroundColor = '#fff';
    elementPickerPopup.style.border = '1px solid #4285f4'; // Match the overlay color
    elementPickerPopup.style.borderRadius = '6px';
    elementPickerPopup.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2), 0 0 0 2px rgba(66, 133, 244, 0.1)'; // Enhanced shadow
    elementPickerPopup.style.padding = '10px';
    elementPickerPopup.style.zIndex = '2147483647'; // Max
    elementPickerPopup.style.fontFamily = 'Arial, sans-serif';
    elementPickerPopup.style.fontSize = '14px';
    elementPickerPopup.style.minWidth = '180px'; // Ensure minimum width
    elementPickerPopup.style.maxWidth = '300px'; // Limit maximum width
    elementPickerPopup.style.transform = 'translateX(-50%)'; // Center horizontally

    // Get element info for the title
    const tagName = element.tagName.toLowerCase();
    const className = element.className ? `.${element.className.split(' ')[0]}` : '';
    const id = element.id ? `#${element.id}` : '';

    // Add title with element info
    const title = document.createElement('div');
    title.innerHTML = `Extract from <span style="color: #4285f4; font-family: monospace;">&lt;${tagName}${id}${className}&gt;</span>:`;
    title.style.fontWeight = 'bold';
    title.style.marginBottom = '12px';
    title.style.borderBottom = '1px solid #eee';
    title.style.paddingBottom = '8px';
    elementPickerPopup.appendChild(title);

    // Add options with icons and descriptions
    const options = [
        {
            label: 'Text Content',
            icon: '📝',
            description: 'Plain text without HTML formatting',
            action: () => extractElementContent(element, 'text')
        },
        {
            label: 'HTML Content',
            icon: '🔧',
            description: 'Raw HTML including tags and formatting',
            action: () => extractElementContent(element, 'html')
        }
    ];

    // Add attribute options if applicable
    if (element.tagName === 'IMG') {
        // For images, always show the src option (even if src is empty)
        options.push({
            label: 'Image URL (src)',
            icon: '🖼️',
            description: 'Insert the image source URL or the image itself',
            action: () => extractElementContent(element, 'src')
        });

        // Only show alt option if the image has alt text
        const altText = element.getAttribute('alt');
        if (altText && altText.trim()) {
            options.push({
                label: 'Image Alt Text',
                icon: '🏷️',
                description: 'Insert the image alternative text',
                action: () => extractElementContent(element, 'alt')
            });
        }
    }

    if (element.tagName === 'A') {
        // For links, always show the href option (even if href is empty)
        options.push({
            label: 'Link URL (href)',
            icon: '🔗',
            description: 'Insert the link destination URL',
            action: () => extractElementContent(element, 'href')
        });
    }

    // Check for background image
    if (element.hasAttribute('data-has-bg-image')) {
        const bgImageUrl = element.getAttribute('data-bg-image-url');
        if (bgImageUrl) {
            options.push({
                label: 'Background Image',
                icon: '🎨',
                description: 'Extract the background image of this element',
                action: () => extractElementContent(element, 'bg-image')
            });
        }
    }

    // Create buttons for each option
    options.forEach(option => {
        const button = document.createElement('button');

        // Create button content with icon, label and description
        const buttonContent = document.createElement('div');
        buttonContent.style.display = 'flex';
        buttonContent.style.flexDirection = 'column';

        // Top row with icon and label
        const topRow = document.createElement('div');
        topRow.style.display = 'flex';
        topRow.style.alignItems = 'center';
        topRow.style.marginBottom = '2px';

        // Icon
        if (option.icon) {
            const icon = document.createElement('span');
            icon.textContent = option.icon;
            icon.style.marginRight = '8px';
            icon.style.fontSize = '16px';
            topRow.appendChild(icon);
        }

        // Label
        const label = document.createElement('span');
        label.textContent = option.label;
        label.style.fontWeight = 'bold';
        topRow.appendChild(label);

        buttonContent.appendChild(topRow);

        // Description
        if (option.description) {
            const description = document.createElement('div');
            description.textContent = option.description;
            description.style.fontSize = '12px';
            description.style.color = '#666';
            description.style.marginLeft = option.icon ? '28px' : '0'; // Indent to align with label
            buttonContent.appendChild(description);
        }

        button.appendChild(buttonContent);

        // Button styling
        button.style.display = 'block';
        button.style.width = '100%';
        button.style.padding = '8px 12px';
        button.style.margin = '6px 0';
        button.style.backgroundColor = '#f8f8f8';
        button.style.border = '1px solid #ddd';
        button.style.borderRadius = '6px';
        button.style.cursor = 'pointer';
        button.style.textAlign = 'left';

        button.addEventListener('mouseover', () => {
            button.style.backgroundColor = '#e8e8e8';
        });

        button.addEventListener('mouseout', () => {
            button.style.backgroundColor = '#f8f8f8';
        });

        button.addEventListener('click', (e) => {
            e.stopPropagation();
            option.action();
            removeElementPickerPopup();
            deactivateElementPicker();
        });

        elementPickerPopup.appendChild(button);
    });

    // Add cancel button
    const cancelButton = document.createElement('button');

    // Create button content with icon and label
    const cancelContent = document.createElement('div');
    cancelContent.style.display = 'flex';
    cancelContent.style.alignItems = 'center';
    cancelContent.style.justifyContent = 'center';

    // Icon
    const cancelIcon = document.createElement('span');
    cancelIcon.textContent = '❌';
    cancelIcon.style.marginRight = '8px';
    cancelIcon.style.fontSize = '14px';
    cancelContent.appendChild(cancelIcon);

    // Label
    const cancelLabel = document.createElement('span');
    cancelLabel.textContent = 'Cancel';
    cancelLabel.style.fontWeight = 'bold';
    cancelContent.appendChild(cancelLabel);

    cancelButton.appendChild(cancelContent);

    // Button styling
    cancelButton.style.display = 'block';
    cancelButton.style.width = '100%';
    cancelButton.style.padding = '8px 12px';
    cancelButton.style.margin = '12px 0 0 0';
    cancelButton.style.backgroundColor = '#f0f0f0';
    cancelButton.style.border = '1px solid #ccc';
    cancelButton.style.borderRadius = '6px';
    cancelButton.style.cursor = 'pointer';
    cancelButton.style.borderTop = '1px solid #eee';
    cancelButton.style.paddingTop = '12px';

    cancelButton.addEventListener('mouseover', () => {
        cancelButton.style.backgroundColor = '#e0e0e0';
    });

    cancelButton.addEventListener('mouseout', () => {
        cancelButton.style.backgroundColor = '#f0f0f0';
    });

    cancelButton.addEventListener('click', (e) => {
        e.stopPropagation();
        removeElementPickerPopup();
        deactivateElementPicker();
    });

    elementPickerPopup.appendChild(cancelButton);

    // Add click outside to close
    document.addEventListener('click', handleElementPickerPopupOutsideClick);

    // Add to document
    document.body.appendChild(elementPickerPopup);

    // Adjust position if popup goes off screen
    const popupRect = elementPickerPopup.getBoundingClientRect();

    // Center the popup horizontally relative to the element
    elementPickerPopup.style.left = `${rect.left + window.scrollX + (rect.width / 2) - (popupRect.width / 2)}px`;

    // Check if popup goes off screen horizontally
    if (popupRect.right > window.innerWidth) {
        elementPickerPopup.style.left = `${window.innerWidth - popupRect.width - 10}px`;
    }
    if (popupRect.left < 0) {
        elementPickerPopup.style.left = '10px';
    }

    // Check if popup goes off screen vertically
    if (popupRect.bottom > window.innerHeight) {
        // If it goes off the bottom, place it above the element instead
        elementPickerPopup.style.top = `${rect.top + window.scrollY - popupRect.height - 10}px`;

        // If it still goes off screen (at the top), place it at the top with some margin
        if (rect.top - popupRect.height < 0) {
            elementPickerPopup.style.top = `${window.scrollY + 10}px`;
        }
    }
}

/**
 * Removes the element picker popup
 */
function removeElementPickerPopup() {
    if (elementPickerPopup) {
        document.removeEventListener('click', handleElementPickerPopupOutsideClick);
        elementPickerPopup.remove();
        elementPickerPopup = null;
    }
}

/**
 * Handles clicks outside the popup to close it
 * @param {MouseEvent} event - The click event
 */
function handleElementPickerPopupOutsideClick(event) {
    if (elementPickerPopup && !elementPickerPopup.contains(event.target)) {
        removeElementPickerPopup();
        deactivateElementPicker();
    }
}

/**
 * Handles mouse movement during element picking
 * @param {MouseEvent} event - The mousemove event
 */
function handleElementPickerMouseMove(event) {
    // Skip if popup is open
    if (elementPickerPopup) return;

    // Get element under cursor, but ignore our own UI elements
    const element = document.elementFromPoint(event.clientX, event.clientY);
    if (!element ||
        element === elementPickerOverlay ||
        element.closest('#Stickara-element-picker-overlay') ||
        element.closest('#Stickara-element-picker-popup') ||
        element.closest(`#${NOTE_ID}`)) {
        // Hide overlay if hovering over our UI
        if (elementPickerOverlay) {
            elementPickerOverlay.style.display = 'none';
        }
        return;
    }

    // Check for special elements that might be more useful than their parent
    // For example, if hovering over text inside a link, we want the link element
    let targetElement = element;

    // Special handling for images inside links - prioritize the image
    if (element.tagName === 'IMG' && element.closest('a')) {
        // If user is specifically hovering over an image inside a link, select the image
        targetElement = element;
    }
    // If element is a text node or has no special properties, try to find a more useful parent
    else if (element.nodeType === Node.TEXT_NODE ||
        (!element.tagName.match(/^(A|IMG|VIDEO|AUDIO|IFRAME|BUTTON|INPUT|SELECT|TEXTAREA)$/i) &&
         !element.getAttribute('src') &&
         !element.getAttribute('href'))) {

        // Check for parent link or image
        const parentLink = element.closest('a');
        const parentImage = element.closest('img');

        if (parentLink) {
            targetElement = parentLink;
        } else if (parentImage) {
            targetElement = parentImage;
        }
    }

    // Special handling for elements with background images
    const computedStyle = window.getComputedStyle(targetElement);
    const backgroundImage = computedStyle.backgroundImage;

    // If the element has a background image and no other special properties, add a data attribute
    // so we can offer to extract the background image
    if (backgroundImage && backgroundImage !== 'none' &&
        !targetElement.hasAttribute('data-has-bg-image')) {

        // Extract the URL from the background-image CSS property
        const bgImageUrl = backgroundImage.replace(/^url\(['"]?/, '').replace(/['"]?\)$/, '');

        if (bgImageUrl) {
            targetElement.setAttribute('data-has-bg-image', 'true');
            targetElement.setAttribute('data-bg-image-url', bgImageUrl);
        }
    }

    // Update highlighted element
    if (targetElement !== elementPickerHighlightedElement) {
        elementPickerHighlightedElement = targetElement;
        updateElementPickerOverlay(targetElement);
    }
}

/**
 * Updates the overlay position and size to match the highlighted element
 * @param {HTMLElement} element - The element to highlight
 */
function updateElementPickerOverlay(element) {
    if (!elementPickerOverlay || !element) return;

    const rect = element.getBoundingClientRect();

    // Calculate the exact position and size including borders and padding
    const left = rect.left + window.scrollX;
    const top = rect.top + window.scrollY;
    const width = rect.width;
    const height = rect.height;

    // Update the overlay position and size
    elementPickerOverlay.style.display = 'block';
    elementPickerOverlay.style.left = `${left}px`;
    elementPickerOverlay.style.top = `${top}px`;
    elementPickerOverlay.style.width = `${width}px`;
    elementPickerOverlay.style.height = `${height}px`;

    // Add element info to the overlay (optional)
    const tagName = element.tagName.toLowerCase();
    const className = element.className ? `.${element.className.split(' ')[0]}` : '';
    const id = element.id ? `#${element.id}` : '';

    // Update the overlay title to show what element is being selected
    elementPickerOverlay.title = `${tagName}${id}${className}`;

    // Add a label to show what element is being selected
    let labelElement = document.getElementById('Stickara-element-picker-label');
    if (!labelElement) {
        labelElement = document.createElement('div');
        labelElement.id = 'Stickara-element-picker-label';
        labelElement.style.position = 'absolute';
        labelElement.style.backgroundColor = '#4285f4';
        labelElement.style.color = 'white';
        labelElement.style.padding = '2px 6px';
        labelElement.style.borderRadius = '3px';
        labelElement.style.fontSize = '12px';
        labelElement.style.fontFamily = 'Arial, sans-serif';
        labelElement.style.pointerEvents = 'none';
        labelElement.style.zIndex = '2147483646';
        labelElement.style.boxShadow = '0 1px 3px rgba(0,0,0,0.3)';
        document.body.appendChild(labelElement);
    }

    // Position the label above the element
    labelElement.textContent = `<${tagName}${id}${className}>`;
    labelElement.style.display = 'block';
    labelElement.style.left = `${left}px`;
    labelElement.style.top = `${top - 20}px`; // 20px above the element

    // If the label would go off the top of the screen, position it below the element
    if (top - 20 < window.scrollY) {
        labelElement.style.top = `${top + height + 4}px`;
    }
}

/**
 * Handles click events during element picking
 * @param {MouseEvent} event - The click event
 */
function handleElementPickerClick(event) {
    // Skip if popup is open
    if (elementPickerPopup) return;

    // Prevent default to avoid navigating links
    event.preventDefault();
    event.stopPropagation();

    // For links and images, we need to be extra careful to prevent navigation
    if (event.target.tagName === 'A' ||
        event.target.closest('a') ||
        event.target.tagName === 'IMG' ||
        event.target.closest('button') ||
        event.target.hasAttribute('onclick') ||
        event.target.getAttribute('role') === 'button') {

        // Double-check to make sure default action is prevented
        setTimeout(() => {
            // If we're still in element picker mode, we're good
            if (elementPickerActive && !elementPickerPopup) {
                // Show a status message to indicate we're preventing navigation
                showStatus('Element selected. Choose what to extract.', 'info');
            }
        }, 50);
    }

    // Get element under cursor, but ignore our own UI elements
    const element = document.elementFromPoint(event.clientX, event.clientY);
    if (!element ||
        element === elementPickerOverlay ||
        element.closest('#Stickara-element-picker-overlay') ||
        element.closest('#Stickara-element-picker-popup') ||
        element.closest(`#${NOTE_ID}`)) {
        return;
    }

    // Check for special elements that might be more useful than their parent
    // For example, if hovering over text inside a link, we want the link element
    let targetElement = element;

    // Special handling for images inside links - prioritize the image
    if (element.tagName === 'IMG' && element.closest('a')) {
        // If user is specifically hovering over an image inside a link, select the image
        targetElement = element;
    }
    // If element is a text node or has no special properties, try to find a more useful parent
    else if (element.nodeType === Node.TEXT_NODE ||
        (!element.tagName.match(/^(A|IMG|VIDEO|AUDIO|IFRAME|BUTTON|INPUT|SELECT|TEXTAREA)$/i) &&
         !element.getAttribute('src') &&
         !element.getAttribute('href'))) {

        // Check for parent link or image
        const parentLink = element.closest('a');
        const parentImage = element.closest('img');

        if (parentLink) {
            targetElement = parentLink;
        } else if (parentImage) {
            targetElement = parentImage;
        }
    }

    // Special handling for elements with background images
    const computedStyle = window.getComputedStyle(targetElement);
    const backgroundImage = computedStyle.backgroundImage;

    // If the element has a background image and no other special properties, add a data attribute
    // so we can offer to extract the background image
    if (backgroundImage && backgroundImage !== 'none' &&
        !targetElement.hasAttribute('data-has-bg-image')) {

        // Extract the URL from the background-image CSS property
        const bgImageUrl = backgroundImage.replace(/^url\(['"]?/, '').replace(/['"]?\)$/, '');

        if (bgImageUrl) {
            targetElement.setAttribute('data-has-bg-image', 'true');
            targetElement.setAttribute('data-bg-image-url', bgImageUrl);
        }
    }

    // Show popup for selecting what to extract
    createElementPickerPopup(targetElement, event);
}

/**
 * Handles keydown events during element picking (Escape to cancel)
 * @param {KeyboardEvent} event - The keydown event
 */
function handleElementPickerKeyDown(event) {
    if (event.key === 'Escape') {
        event.preventDefault();
        event.stopPropagation();
        deactivateElementPicker();
    }
}

/**
 * Prevents default actions on links, images, and other clickable elements during element picking
 * @param {MouseEvent} event - The mouse event
 */
function preventDefaultForElementPicker(event) {
    if (!elementPickerActive) return;

    // Skip if the event is on our own UI elements
    if (event.target === elementPickerOverlay ||
        event.target.closest('#Stickara-element-picker-overlay') ||
        event.target.closest('#Stickara-element-picker-popup') ||
        event.target.closest(`#${NOTE_ID}`)) {
        return;
    }

    // Get the target element or its closest clickable parent
    const target = event.target;
    const clickableParent = target.closest('a, button, input[type="button"], input[type="submit"], [onclick], [role="button"]');

    // If the target or its parent is a clickable element, prevent the default action
    if (target.tagName === 'A' ||
        target.tagName === 'IMG' ||
        target.tagName === 'BUTTON' ||
        target.tagName === 'INPUT' ||
        target.hasAttribute('onclick') ||
        target.getAttribute('role') === 'button' ||
        clickableParent) {

        event.preventDefault();
        event.stopPropagation();
    }
}

/**
 * Extracts content from an element and inserts it into the note
 * @param {HTMLElement} element - The element to extract content from
 * @param {string} type - The type of content to extract ('text', 'html', 'src', 'href', 'alt')
 */
function extractElementContent(element, type) {
    if (!element) return;

    let content = '';

    switch (type) {
        case 'text':
            content = element.textContent.trim();
            break;
        case 'html':
            content = element.innerHTML.trim();
            break;
        case 'src':
            // Get the src attribute and ensure it's an absolute URL
            let srcAttr = element.getAttribute('src') || '';
            if (srcAttr) {
                // Convert relative URLs to absolute
                try {
                    content = new URL(srcAttr, window.location.href).href;
                } catch (e) {
                    console.warn("Stickara: Error converting relative URL to absolute:", e);
                    content = srcAttr; // Use original if conversion fails
                }
            }
            break;
        case 'href':
            // Get the href attribute and ensure it's an absolute URL
            let hrefAttr = element.getAttribute('href') || '';
            if (hrefAttr) {
                // Convert relative URLs to absolute
                try {
                    content = new URL(hrefAttr, window.location.href).href;
                } catch (e) {
                    console.warn("Stickara: Error converting relative URL to absolute:", e);
                    content = hrefAttr; // Use original if conversion fails
                }
            }
            break;
        case 'bg-image':
            // Get the background image URL from the data attribute
            let bgImageUrl = element.getAttribute('data-bg-image-url') || '';
            if (bgImageUrl) {
                // Convert relative URLs to absolute
                try {
                    // Remove quotes if present
                    bgImageUrl = bgImageUrl.replace(/^['"]/, '').replace(/['"]$/, '');
                    content = new URL(bgImageUrl, window.location.href).href;
                } catch (e) {
                    console.warn("Stickara: Error converting relative URL to absolute:", e);
                    content = bgImageUrl; // Use original if conversion fails
                }
            }
            break;
        case 'alt':
            content = element.getAttribute('alt') || '';
            break;
        default:
            content = element.textContent.trim();
    }

    // Insert content into note
    if (content) {
        // Show note if hidden
        if (!noteContainer || !noteText || !noteContainer.classList.contains('visible')) {
            showNote();
            // Use timeout to ensure note is visible before inserting
            setTimeout(() => insertPickedContent(content, type), 150);
        } else {
            insertPickedContent(content, type);
        }
    } else {
        showStatus('No content found to extract.', 'warning');
    }
}

/**
 * Inserts the picked content into the note
 * @param {string} content - The content to insert
 * @param {string} type - The type of content ('text', 'html', 'src', 'href', 'alt')
 */
function insertPickedContent(content, type) {
    if (!content || !noteText) return;

    let htmlToInsert = '';

    switch (type) {
        case 'text':
            // Escape HTML characters to prevent XSS
            const escapedText = content.replace(/</g, "&lt;").replace(/>/g, "&gt;");
            htmlToInsert = escapedText;
            break;
        case 'html':
            // For HTML content, wrap in a styled div to distinguish it
            htmlToInsert = `<div class="Stickara-picked-html" style="border-left: 3px solid #4285f4; padding-left: 10px; margin: 5px 0;">${content}</div>`;
            break;
        case 'src':
        case 'bg-image':
            // For image URLs (src or background-image), create an img tag
            // Check if the URL is valid and looks like an image
            if (content.match(/\.(jpeg|jpg|gif|png|webp|svg|avif)(\?.*)?$/i) ||
                content.includes('/image/') ||
                content.startsWith('data:image/')) {
                // It looks like an image URL, insert as an image
                const sourceType = type === 'bg-image' ? 'background image' : 'image';
                htmlToInsert = `<img src="${content}" alt="Picked ${sourceType}" class="Stickara-inserted-image" style="max-width: 95%; max-height: 300px; display: block; margin: 10px auto; border-radius: 4px;" contenteditable="false">`;
            } else {
                // It doesn't look like an image URL, insert as a link
                // Add contenteditable="false" to make the link clickable in the note editor
                htmlToInsert = `<a href="${content}" target="_blank" rel="noopener noreferrer" contenteditable="false">${content}</a>`;
            }
            break;
        case 'href':
            // For links, create an anchor tag with the URL as both href and text
            // Add contenteditable="false" to make the link clickable in the note editor
            htmlToInsert = `<a href="${content}" target="_blank" rel="noopener noreferrer" contenteditable="false">${content}</a>`;
            break;
        case 'alt':
            // For alt text, just insert as plain text
            const escapedAlt = escapeHtml(content);
            htmlToInsert = escapedAlt;
            break;
        default:
            // Default case, escape HTML characters
            const escapedDefault = escapeHtml(content);
            htmlToInsert = escapedDefault;
    }

    // Insert the content
    insertHtmlAtCursor(htmlToInsert);

    // Show feedback
    showStatus('Content added to note.', 'success');
}

/**
 * Shows a status message to the user
 * @param {string} message - The message to show
 * @param {string} type - The type of message ('info', 'success', 'warning', 'error')
 */
function showStatus(message, type = 'info') {
    // Create status element if it doesn't exist
    let statusElement = document.getElementById('Stickara-status');
    if (!statusElement) {
        statusElement = document.createElement('div');
        statusElement.id = 'Stickara-status';
        statusElement.style.position = 'fixed';
        statusElement.style.bottom = '20px';
        statusElement.style.right = '20px';
        statusElement.style.padding = '10px 15px';
        statusElement.style.borderRadius = '4px';
        statusElement.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
        statusElement.style.zIndex = '2147483647'; // Max
        statusElement.style.fontFamily = 'Arial, sans-serif';
        statusElement.style.fontSize = '14px';
        statusElement.style.transition = 'opacity 0.3s ease-in-out';
        document.body.appendChild(statusElement);
    }

    // Set style based on type
    switch (type) {
        case 'success':
            statusElement.style.backgroundColor = '#4caf50';
            statusElement.style.color = '#fff';
            break;
        case 'warning':
            statusElement.style.backgroundColor = '#ff9800';
            statusElement.style.color = '#fff';
            break;
        case 'error':
            statusElement.style.backgroundColor = '#f44336';
            statusElement.style.color = '#fff';
            break;
        case 'info':
        default:
            statusElement.style.backgroundColor = '#2196f3';
            statusElement.style.color = '#fff';
    }

    // Set message
    statusElement.textContent = message;

    // Show status
    statusElement.style.opacity = '1';

    // Clear any existing timeout
    if (statusElement.timeout) {
        clearTimeout(statusElement.timeout);
    }

    // Hide after 3 seconds
    statusElement.timeout = setTimeout(() => {
        statusElement.style.opacity = '0';
        setTimeout(() => {
            if (statusElement.parentNode) {
                statusElement.parentNode.removeChild(statusElement);
            }
        }, 300);
    }, 3000);
}

/**
 * Sets the font size for the selected text or at the current cursor position.
 * If no text is selected, it will apply to text typed at the current cursor position.
 * Preserves existing font family and other styling.
 * @param {number} size - The font size in pixels to set.
 */
function setFontSize(size) {
    if (!noteText) return;

    // Ensure size is a valid number between 10 and 48 pixels
    const newSize = Math.max(10, Math.min(size, 48));

    noteText.focus(); // Ensure the note text area has focus

    const sel = window.getSelection();
    const hasSelection = sel && sel.rangeCount > 0 && !sel.getRangeAt(0).collapsed;

    if (hasSelection) {
        // There is a selection, apply font size to it
        try {
            applyStyleToSelection({ fontSize: newSize + 'px' });
            console.log("Font size applied to selection:", newSize + "px");
        } catch (e) {
            console.error("Stickara: Error applying font size to selection:", e);
        }
    } else {
        // No selection, create a span at cursor position for future typing
        try {
            // Get current styles at cursor position
            const currentStyles = getCurrentStylesAtCursor();
            currentStyles.fontSize = newSize + 'px';

            // Create a span element with the combined styles
            const span = document.createElement('span');
            Object.assign(span.style, currentStyles);

            // Add a zero-width space to make the span visible
            span.innerHTML = '&#8203;'; // Zero-width space

            // Insert the span at the cursor position using modern approach
            const selection = window.getSelection();
            if (selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);
                range.deleteContents();
                const fragment = range.createContextualFragment(span.outerHTML);
                range.insertNode(fragment);
                range.collapse(false);
                selection.removeAllRanges();
                selection.addRange(range);
            }

            console.log("Font size span inserted at cursor:", newSize + "px");
        } catch (e) {
            console.error("Stickara: Error inserting font size span at cursor:", e);
        }
    }

    scheduleSave();
}

/**
 * Sets the font family for the selected text or at the current cursor position.
 * If no text is selected, it will apply to text typed at the current cursor position.
 * Preserves existing font size and other styling.
 * @param {string} fontFamily - The font family to set.
 */
function setFontFamily(fontFamily) {
    if (!noteText) return;

    noteText.focus(); // Ensure the note text area has focus

    const sel = window.getSelection();
    const hasSelection = sel && sel.rangeCount > 0 && !sel.getRangeAt(0).collapsed;

    if (hasSelection) {
        // There is a selection, apply font family to it
        try {
            const styleToApply = fontFamily === 'default' ? { fontFamily: '' } : { fontFamily: fontFamily };
            applyStyleToSelection(styleToApply);
            console.log("Font family applied to selection:", fontFamily);
        } catch (e) {
            console.error("Stickara: Error applying font family to selection:", e);
        }
    } else {
        // No selection, create a span at cursor position for future typing
        try {
            // Get current styles at cursor position
            const currentStyles = getCurrentStylesAtCursor();
            if (fontFamily === 'default') {
                currentStyles.fontFamily = '';
            } else {
                currentStyles.fontFamily = fontFamily;
            }

            // Create a span element with the combined styles
            const span = document.createElement('span');
            Object.assign(span.style, currentStyles);

            // Add a zero-width space to make the span visible
            span.innerHTML = '&#8203;'; // Zero-width space

            // Insert the span at the cursor position using modern approach
            const selection = window.getSelection();
            if (selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);
                range.deleteContents();
                const fragment = range.createContextualFragment(span.outerHTML);
                range.insertNode(fragment);
                range.collapse(false);
                selection.removeAllRanges();
                selection.addRange(range);
            }

            console.log("Font family span inserted at cursor:", fontFamily);
        } catch (e) {
            console.error("Stickara: Error inserting font family span at cursor:", e);
        }
    }

    scheduleSave();
}

/**
 * Gets the current styles at the cursor position by examining parent elements.
 * @returns {Object} Object containing current styles (fontSize, fontFamily, etc.)
 */
function getCurrentStylesAtCursor() {
    const styles = {};
    const sel = window.getSelection();

    if (sel && sel.rangeCount > 0) {
        const range = sel.getRangeAt(0);
        let element = range.commonAncestorContainer;

        // If it's a text node, get its parent element
        if (element.nodeType === Node.TEXT_NODE) {
            element = element.parentElement;
        }

        // Traverse up the DOM tree to collect styles
        while (element && element !== noteText) {
            // Collect font-related styles
            if (element.style.fontSize && !styles.fontSize) {
                styles.fontSize = element.style.fontSize;
            }
            if (element.style.fontFamily && !styles.fontFamily) {
                styles.fontFamily = element.style.fontFamily;
            }
            if (element.style.fontWeight && !styles.fontWeight) {
                styles.fontWeight = element.style.fontWeight;
            }
            if (element.style.fontStyle && !styles.fontStyle) {
                styles.fontStyle = element.style.fontStyle;
            }
            if (element.style.textDecoration && !styles.textDecoration) {
                styles.textDecoration = element.style.textDecoration;
            }

            element = element.parentElement;
        }
    }

    return styles;
}

/**
 * Applies styles to the current selection while preserving existing styles.
 * @param {Object} stylesToApply - Object containing CSS styles to apply
 */
function applyStyleToSelection(stylesToApply) {
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0 || sel.getRangeAt(0).collapsed) return;

    const range = sel.getRangeAt(0);

    // Check if selection is within a single styled span
    const commonAncestor = range.commonAncestorContainer;
    let parentSpan = null;

    if (commonAncestor.nodeType === Node.ELEMENT_NODE && commonAncestor.tagName === 'SPAN') {
        parentSpan = commonAncestor;
    } else if (commonAncestor.parentElement && commonAncestor.parentElement.tagName === 'SPAN') {
        parentSpan = commonAncestor.parentElement;
    }

    if (parentSpan && range.startContainer === range.endContainer) {
        // Selection is within a single span, just update its styles
        Object.assign(parentSpan.style, stylesToApply);
    } else {
        // Selection spans multiple elements or is not in a span
        // Get current styles at the selection
        const currentStyles = getCurrentStylesAtCursor();

        // Merge with new styles
        const combinedStyles = { ...currentStyles, ...stylesToApply };

        // Create a new span with combined styles
        const span = document.createElement('span');
        Object.assign(span.style, combinedStyles);

        // Extract the selected content
        const selectedContent = range.extractContents();
        span.appendChild(selectedContent);

        // Insert the styled span
        range.insertNode(span);

        // Restore selection
        const newRange = document.createRange();
        newRange.selectNodeContents(span);
        sel.removeAllRanges();
        sel.addRange(newRange);
    }
}

/**
 * Sets the global font size for the entire note text area.
 * This affects the base font size for all new text.
 * @param {number} size - The font size in pixels to set.
 */
function setGlobalFontSize(size) {
    if (!noteText) return;

    // Ensure size is a valid number between 10 and 24 pixels
    const newSize = Math.max(10, Math.min(size, 24));

    // Set the CSS custom property for the note text area
    noteText.style.setProperty('--Stickara-default-font-size', newSize + 'px');
    noteText.style.fontSize = newSize + 'px';

    // Save the global font size preference
    if (notes && notes[currentNoteIndex]) {
        notes[currentNoteIndex].globalFontSize = newSize;
        notes[currentNoteIndex].preferredFontSize = newSize;
    }

    // Update the default font size constant for new notes
    if (typeof updateDefaultFontSize === 'function') {
        updateDefaultFontSize(newSize);
    }

    console.log("Global font size set to:", newSize + "px");
    scheduleSave();
}

/**
 * Updates the global font size slider to reflect the current value.
 * @param {number} size - The font size to set on the slider.
 */
function updateGlobalFontSizeSlider(size) {
    const slider = document.querySelector('.Stickara-font-size-slider');
    const valueDisplay = slider?.parentElement?.querySelector('span');

    if (slider) {
        slider.value = size;
    }

    if (valueDisplay) {
        valueDisplay.textContent = size + 'px';
    }
}






/**
 * Detects if an element contains a video timestamp pattern.
 * Supports various timestamp formats commonly found on video websites.
 * @param {HTMLElement} element - The element to check for timestamp patterns
 * @returns {Object|null} Object with timestamp info or null if no timestamp found
 */
function detectVideoTimestamp(element) {
    if (!element) return null;

    const text = element.textContent || element.innerText || '';

    // Common timestamp patterns
    const timestampPatterns = [
        // MM:SS format (e.g., "5:30", "12:45")
        /\b(\d{1,2}):([0-5]\d)\b/g,
        // HH:MM:SS format (e.g., "1:23:45", "0:05:30")
        /\b(\d{1,2}):([0-5]\d):([0-5]\d)\b/g,
        // Timestamp with brackets (e.g., "[5:30]", "(12:45)")
        /[\[\(](\d{1,2}):([0-5]\d)[\]\)]/g,
        /[\[\(](\d{1,2}):([0-5]\d):([0-5]\d)[\]\)]/g,
        // Timestamp with @ symbol (e.g., "@5:30", "@1:23:45")
        /@(\d{1,2}):([0-5]\d)/g,
        /@(\d{1,2}):([0-5]\d):([0-5]\d)/g,
        // Timestamp with "at" prefix (e.g., "at 5:30", "at 1:23:45")
        /\bat\s+(\d{1,2}):([0-5]\d)/gi,
        /\bat\s+(\d{1,2}):([0-5]\d):([0-5]\d)/gi
    ];

    for (const pattern of timestampPatterns) {
        const matches = text.matchAll(pattern);
        for (const match of matches) {
            let hours = 0, minutes = 0, seconds = 0;

            if (match.length === 3) {
                // MM:SS format
                minutes = parseInt(match[1], 10);
                seconds = parseInt(match[2], 10);
            } else if (match.length === 4) {
                // HH:MM:SS format
                hours = parseInt(match[1], 10);
                minutes = parseInt(match[2], 10);
                seconds = parseInt(match[3], 10);
            }

            // Validate timestamp values
            if (minutes < 60 && seconds < 60) {
                const totalSeconds = hours * 3600 + minutes * 60 + seconds;
                const formattedTime = formatSecondsToMMSS(totalSeconds);

                return {
                    originalText: match[0],
                    totalSeconds: totalSeconds,
                    formattedTime: formattedTime,
                    hours: hours,
                    minutes: minutes,
                    seconds: seconds
                };
            }
        }
    }

    return null;
}

/**
 * Converts a timestamp string to seconds.
 * @param {string} timestampStr - Timestamp string (e.g., "5:30", "1:23:45")
 * @returns {number} Total seconds or 0 if invalid
 */
function parseTimestampToSeconds(timestampStr) {
    if (!timestampStr) return 0;

    // Clean the timestamp string
    const cleanStr = timestampStr.replace(/[\[\]()@]/g, '').replace(/^at\s+/i, '').trim();

    // Split by colon
    const parts = cleanStr.split(':').map(part => parseInt(part, 10));

    if (parts.length === 2) {
        // MM:SS format
        const [minutes, seconds] = parts;
        if (minutes >= 0 && seconds >= 0 && seconds < 60) {
            return minutes * 60 + seconds;
        }
    } else if (parts.length === 3) {
        // HH:MM:SS format
        const [hours, minutes, seconds] = parts;
        if (hours >= 0 && minutes >= 0 && minutes < 60 && seconds >= 0 && seconds < 60) {
            return hours * 3600 + minutes * 60 + seconds;
        }
    }

    return 0;
}

console.log("Stickara: Utils Loaded (v4.5 - Core Functionality)"); // Updated log
// --- END OF FILE content-utils.js ---