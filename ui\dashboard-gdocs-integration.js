/**
 * Dashboard Google Docs Integration for Stickara
 * 
 * This module integrates Google Docs export functionality with the Stickara dashboard.
 */

// Create a namespace to avoid global pollution
window.StickaraDashboardGDocsIntegration = (function() {
    // Private variables
    let isInitialized = false;
    let initInterval = null;
    
    /**
     * Initializes the dashboard Google Docs integration
     * @returns {Promise<boolean>} A promise that resolves to true if initialization was successful
     */
    async function init() {
        try {
            // Check if already initialized
            if (isInitialized) {
                return true;
            }
            
            console.log("Stickara: Initializing Dashboard Google Docs Integration");
            
            // Start polling for dashboard readiness
            startDashboardPolling();
            
            // Add direct event listeners for checkbox changes
            addDirectEventListeners();
            
            isInitialized = true;
            return true;
        } catch (error) {
            console.error('Error initializing dashboard Google Docs integration:', error);
            return false;
        }
    }
    
    /**
     * Starts polling for dashboard readiness
     */
    function startDashboardPolling() {
        if (initInterval) {
            clearInterval(initInterval);
        }
        
        initInterval = setInterval(() => {
            // Check if dashboard container exists
            const dashboardContainer = document.querySelector('.stickara-dashboard-container');
            if (dashboardContainer) {
                addExportButtonToDashboard();
                
                // Once we've successfully added the button, stop polling
                if (document.getElementById('stickara-dashboard-gdocs-btn')) {
                    clearInterval(initInterval);
                    console.log("Stickara: Google Docs export button added to dashboard");
                }
            }
        }, 1000);
        
        // Stop polling after 30 seconds to prevent indefinite polling
        setTimeout(() => {
            if (initInterval) {
                clearInterval(initInterval);
                console.log("Stickara: Stopped dashboard polling after timeout");
            }
        }, 30000);
    }
    
    /**
     * Adds direct event listeners for checkbox changes
     */
    function addDirectEventListeners() {
        // Listen for checkbox changes directly
        document.addEventListener('change', function(event) {
            // Check if the changed element is a checkbox in the dashboard
            const target = event.target;
            if (target.type === 'checkbox' && (
                target.closest('.stickara-dashboard-item') || 
                target.id === 'stickara-select-all' ||
                target.classList.contains('stickara-item-checkbox')
            )) {
                console.log("Stickara: Dashboard checkbox changed");
                setTimeout(updateExportButtonVisibility, 100);
            }
        });
        
        // Also check periodically in case the dashboard uses a different selection mechanism
        setInterval(updateExportButtonVisibility, 2000);
        
        // Add click handler for the export button
        document.addEventListener('click', function(event) {
            if (event.target.id === 'stickara-dashboard-gdocs-btn' || event.target.closest('#stickara-dashboard-gdocs-btn')) {
                handleExportButtonClick();
            }
        });
    }
    
    /**
     * Updates the visibility of the export button based on selected items
     */
    function updateExportButtonVisibility() {
        const exportButton = document.getElementById('stickara-dashboard-gdocs-btn');
        if (!exportButton) return;
        
        // Check for selected items
        const selectedItems = getSelectedItems();
        
        if (selectedItems.length > 0) {
            console.log("Stickara: Found selected items, showing export button");
            exportButton.style.display = 'inline-block';
        } else {
            exportButton.style.display = 'none';
        }
    }
    
    /**
     * Gets selected items from the dashboard
     * @returns {Array} An array of selected items
     */
    function getSelectedItems() {
        // Try to get from StickaraDashboardEvents if available
        if (window.StickaraDashboardEvents && typeof window.StickaraDashboardEvents.getSelectedItems === 'function') {
            return window.StickaraDashboardEvents.getSelectedItems();
        }
        
        // Fallback to checking for checked checkboxes directly
        const checkedCheckboxes = document.querySelectorAll('.stickara-item-checkbox:checked, .stickara-dashboard-item input[type="checkbox"]:checked');
        
        if (checkedCheckboxes.length === 0) {
            return [];
        }
        
        // Map checkboxes to items
        return Array.from(checkedCheckboxes).map(checkbox => {
            const itemElement = checkbox.closest('.stickara-dashboard-item');
            if (!itemElement) return null;
            
            // Get item data
            const itemId = itemElement.dataset.itemId || itemElement.id || '';
            const itemType = itemElement.dataset.itemType || (itemElement.classList.contains('note-item') ? 'note' : 'highlight');
            const itemUrl = itemElement.dataset.itemUrl || '';
            const itemText = itemElement.querySelector('.stickara-item-text, .item-content')?.textContent || '';
            
            return {
                id: itemId,
                type: itemType,
                url: itemUrl,
                text: itemText
            };
        }).filter(Boolean); // Remove null items
    }
    
    /**
     * Adds the export button to the dashboard
     */
    function addExportButtonToDashboard() {
        // Check for actions container or create one if needed
        let actionsContainer = document.querySelector('.stickara-dashboard-actions');
        
        if (!actionsContainer) {
            // Look for alternate container structures
            const dashboardHeader = document.querySelector('.stickara-dashboard-header, .dashboard-header');
            if (dashboardHeader) {
                actionsContainer = document.createElement('div');
                actionsContainer.className = 'stickara-dashboard-actions';
                dashboardHeader.appendChild(actionsContainer);
            } else {
                // Create a new actions container and add it to the dashboard
                const dashboardContainer = document.querySelector('.stickara-dashboard-container, .dashboard-container');
                if (dashboardContainer) {
                    actionsContainer = document.createElement('div');
                    actionsContainer.className = 'stickara-dashboard-actions';
                    actionsContainer.style.padding = '10px';
                    actionsContainer.style.display = 'flex';
                    actionsContainer.style.justifyContent = 'flex-end';
                    actionsContainer.style.borderBottom = '1px solid #eee';
                    dashboardContainer.insertBefore(actionsContainer, dashboardContainer.firstChild);
                } else {
                    console.warn('Stickara: Dashboard container not found, cannot add actions container');
                    return;
                }
            }
        }
        
        // Check if button already exists
        if (document.getElementById('stickara-dashboard-gdocs-btn')) {
            return;
        }
        
        console.log("Stickara: Creating Google Docs export button");
        
        // Create export button
        const exportButton = document.createElement('button');
        exportButton.id = 'stickara-dashboard-gdocs-btn';
        exportButton.className = 'stickara-dashboard-action-btn';
        exportButton.title = 'Export selected items to Google Docs';
        exportButton.innerHTML = '<span class="stickara-icon">📄</span> Export to Google Docs';
        
        // Style the button
        exportButton.style.display = 'none'; // Hidden by default, shown when items are selected
        exportButton.style.backgroundColor = '#4285F4'; // Google blue
        exportButton.style.color = 'white';
        exportButton.style.border = 'none';
        exportButton.style.borderRadius = '4px';
        exportButton.style.padding = '8px 12px';
        exportButton.style.margin = '5px';
        exportButton.style.cursor = 'pointer';
        exportButton.style.fontWeight = 'bold';
        exportButton.style.fontSize = '14px';
        exportButton.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.2)';
        exportButton.style.transition = 'all 0.2s ease';
        exportButton.style.zIndex = '1000'; // Ensure it's above other elements
        
        // Add hover effect
        exportButton.addEventListener('mouseover', function() {
            this.style.backgroundColor = '#3367D6'; // Darker Google blue
            this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
        });
        
        exportButton.addEventListener('mouseout', function() {
            this.style.backgroundColor = '#4285F4';
            this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.2)';
        });
        
        // Add click handler
        exportButton.addEventListener('click', handleExportButtonClick);
        
        // Add to dashboard
        actionsContainer.appendChild(exportButton);
        
        // Check if there are already selected items
        updateExportButtonVisibility();
    }
    
    /**
     * Handles export button click
     */
    function handleExportButtonClick() {
        console.log("Stickara: Export button clicked");
        
        // Show export modal if available
        if (window.StickaraGoogleDocsExport && typeof window.StickaraGoogleDocsExport.showExportModal === 'function') {
            window.StickaraGoogleDocsExport.showExportModal();
        } else {
            // If the export UI is not available, create a simple modal
            showSimpleExportModal();
        }
    }
    
    /**
     * Shows a simple export modal as a fallback
     */
    function showSimpleExportModal() {
        // Check if modal already exists
        let modal = document.getElementById('stickara-simple-export-modal');
        
        if (!modal) {
            // Create modal
            modal = document.createElement('div');
            modal.id = 'stickara-simple-export-modal';
            modal.style.position = 'fixed';
            modal.style.top = '0';
            modal.style.left = '0';
            modal.style.width = '100%';
            modal.style.height = '100%';
            modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            modal.style.display = 'flex';
            modal.style.justifyContent = 'center';
            modal.style.alignItems = 'center';
            modal.style.zIndex = '10000';
            
            // Create modal content
            const modalContent = document.createElement('div');
            modalContent.style.backgroundColor = 'white';
            modalContent.style.padding = '20px';
            modalContent.style.borderRadius = '8px';
            modalContent.style.maxWidth = '400px';
            modalContent.style.width = '90%';
            modalContent.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
            
            modalContent.innerHTML = `
                <h3 style="margin-top: 0; color: #4285F4;">Export to Google Docs</h3>
                <p>Do you want to export the selected items to Google Docs?</p>
                <div style="display: flex; justify-content: flex-end; margin-top: 20px;">
                    <button id="stickara-simple-export-cancel" style="margin-right: 10px; padding: 8px 16px; background-color: #f1f1f1; border: none; border-radius: 4px; cursor: pointer;">Cancel</button>
                    <button id="stickara-simple-export-confirm" style="padding: 8px 16px; background-color: #4285F4; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">Export</button>
                </div>
            `;
            
            modal.appendChild(modalContent);
            document.body.appendChild(modal);
            
            // Add event listeners
            document.getElementById('stickara-simple-export-cancel').addEventListener('click', function() {
                modal.style.display = 'none';
            });
            
            document.getElementById('stickara-simple-export-confirm').addEventListener('click', function() {
                modal.style.display = 'none';
                alert('This feature is coming soon! The Google Docs export functionality is currently being implemented.');
            });
            
            // Close modal when clicking outside
            modal.addEventListener('click', function(event) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        } else {
            // Show existing modal
            modal.style.display = 'flex';
        }
    }
    
    // Return the public API
    return {
        init,
        isInitialized: () => isInitialized,
        getSelectedItems,
        updateExportButtonVisibility
    };
})();

// Initialize immediately
window.StickaraDashboardGDocsIntegration.init();

// Also initialize when the document is ready
document.addEventListener('DOMContentLoaded', function() {
    window.StickaraDashboardGDocsIntegration.init();
});

// Also initialize when the window loads
window.addEventListener('load', function() {
    window.StickaraDashboardGDocsIntegration.init();
});

console.log("Stickara: Dashboard Google Docs Integration Module Loaded");
