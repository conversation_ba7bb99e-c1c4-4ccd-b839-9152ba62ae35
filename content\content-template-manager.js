// --- START OF FILE content-template-manager.js ---

/**
 * Template Manager for Stickara Smart Templates
 * Handles storage, retrieval, and management of custom templates
 */

// Template storage keys
const CUSTOM_TEMPLATES_KEY = 'Stickara_custom_templates';
const TEMPLATE_METADATA_KEY = 'Stickara_template_metadata';
const RECENT_TEMPLATES_KEY = 'Stickara_recent_templates';

// Template categories
const TEMPLATE_CATEGORIES = {
    'general': '📝 General',
    'work': '💼 Work',
    'study': '📚 Study',
    'personal': '👤 Personal',
    'project': '🚀 Project',
    'meeting': '🤝 Meeting',
    'custom': '⚙️ Custom'
};

/**
 * Template Manager Class
 */
class StickaraTemplateManager {
    constructor() {
        this.customTemplates = {};
        this.templateMetadata = {};
        this.recentTemplates = [];
        this.loadTemplates();
    }

    /**
     * Load templates from storage
     */
    loadTemplates() {
        try {
            // Load custom templates
            const customData = localStorage.getItem(CUSTOM_TEMPLATES_KEY);
            this.customTemplates = customData ? JSON.parse(customData) : {};

            // Load template metadata
            const metadataData = localStorage.getItem(TEMPLATE_METADATA_KEY);
            this.templateMetadata = metadataData ? JSON.parse(metadataData) : {};

            // Load recent templates
            const recentData = localStorage.getItem(RECENT_TEMPLATES_KEY);
            this.recentTemplates = recentData ? JSON.parse(recentData) : [];

            console.log('Stickara: Templates loaded successfully');
        } catch (error) {
            console.error('Stickara: Error loading templates:', error);
            this.customTemplates = {};
            this.templateMetadata = {};
            this.recentTemplates = [];
        }
    }

    /**
     * Save templates to storage
     */
    saveTemplates() {
        try {
            localStorage.setItem(CUSTOM_TEMPLATES_KEY, JSON.stringify(this.customTemplates));
            localStorage.setItem(TEMPLATE_METADATA_KEY, JSON.stringify(this.templateMetadata));
            localStorage.setItem(RECENT_TEMPLATES_KEY, JSON.stringify(this.recentTemplates));
            console.log('Stickara: Templates saved successfully');
            return true;
        } catch (error) {
            console.error('Stickara: Error saving templates:', error);
            return false;
        }
    }

    /**
     * Add or update a custom template
     */
    saveTemplate(name, content, metadata = {}) {
        if (!name || !content) {
            throw new Error('Template name and content are required');
        }

        // Validate template content
        if (!this.validateTemplate(content)) {
            throw new Error('Invalid template syntax');
        }

        // Create template metadata
        const templateMeta = {
            category: metadata.category || 'custom',
            description: metadata.description || '',
            tags: metadata.tags || [],
            created: metadata.created || new Date().toISOString(),
            modified: new Date().toISOString(),
            usage: this.templateMetadata[name]?.usage || 0,
            isCustom: true
        };

        // Save template and metadata
        this.customTemplates[name] = content;
        this.templateMetadata[name] = templateMeta;

        // Update recent templates
        this.addToRecent(name);

        // Save to storage
        if (this.saveTemplates()) {
            // Refresh template dropdown
            this.refreshTemplateDropdown();
            return true;
        }
        return false;
    }

    /**
     * Delete a custom template
     */
    deleteTemplate(name) {
        if (!this.customTemplates[name]) {
            throw new Error('Template not found');
        }

        delete this.customTemplates[name];
        delete this.templateMetadata[name];

        // Remove from recent templates
        this.recentTemplates = this.recentTemplates.filter(t => t !== name);

        // Save to storage
        if (this.saveTemplates()) {
            this.refreshTemplateDropdown();
            return true;
        }
        return false;
    }

    /**
     * Get a template by name
     */
    getTemplate(name) {
        // Check custom templates first
        if (this.customTemplates[name]) {
            return {
                content: this.customTemplates[name],
                metadata: this.templateMetadata[name],
                isCustom: true
            };
        }

        // Check built-in templates
        if (typeof StickaraTemplates === 'object' && StickaraTemplates[name]) {
            return {
                content: StickaraTemplates[name],
                metadata: { isCustom: false },
                isCustom: false
            };
        }

        return null;
    }

    /**
     * Get all templates (built-in + custom)
     */
    getAllTemplates() {
        const templates = {};

        // Add built-in templates
        if (typeof StickaraTemplates === 'object') {
            Object.keys(StickaraTemplates).forEach(name => {
                templates[name] = {
                    content: StickaraTemplates[name],
                    metadata: { isCustom: false, category: this.categorizeBuiltInTemplate(name) },
                    isCustom: false
                };
            });
        }

        // Add custom templates
        Object.keys(this.customTemplates).forEach(name => {
            templates[name] = {
                content: this.customTemplates[name],
                metadata: this.templateMetadata[name] || { isCustom: true },
                isCustom: true
            };
        });

        return templates;
    }

    /**
     * Categorize built-in templates based on name
     */
    categorizeBuiltInTemplate(name) {
        const lowerName = name.toLowerCase();

        // Academic/Study templates
        if (lowerName.includes('lecture') || lowerName.includes('study') ||
            lowerName.includes('assignment') || lowerName.includes('reading') ||
            lowerName.includes('grade') || lowerName.includes('schedule')) return 'study';

        // Work/Professional templates
        if (lowerName.includes('meeting') || lowerName.includes('project') ||
            lowerName.includes('bug') || lowerName.includes('office hours') ||
            lowerName.includes('rubric') || lowerName.includes('publication')) return 'work';

        // Research/Science templates
        if (lowerName.includes('experiment') || lowerName.includes('literature') ||
            lowerName.includes('data analysis') || lowerName.includes('lab') ||
            lowerName.includes('sample') || lowerName.includes('reagent') ||
            lowerName.includes('protocol')) return 'project';

        // Personal templates
        if (lowerName.includes('journal') || lowerName.includes('daily') ||
            lowerName.includes('shopping') || lowerName.includes('event') ||
            lowerName.includes('goal') || lowerName.includes('recipe') ||
            lowerName.includes('workout')) return 'personal';

        // General purpose templates
        return 'general';
    }

    /**
     * Add template to recent list
     */
    addToRecent(name) {
        // Remove if already exists
        this.recentTemplates = this.recentTemplates.filter(t => t !== name);

        // Add to beginning
        this.recentTemplates.unshift(name);

        // Keep only last 10
        this.recentTemplates = this.recentTemplates.slice(0, 10);
    }

    /**
     * Validate template syntax
     */
    validateTemplate(content) {
        try {
            // Check for balanced braces
            const openBraces = (content.match(/\{\{/g) || []).length;
            const closeBraces = (content.match(/\}\}/g) || []).length;

            if (openBraces !== closeBraces) {
                return false;
            }

            // Check for valid syntax patterns
            const invalidPatterns = [
                /\{\{[^}]*\{/,  // Nested opening braces
                /\}[^{]*\}\}/,  // Nested closing braces
                /\{\{[^:}]*:[^}]*\{/,  // Invalid prompt syntax
            ];

            for (const pattern of invalidPatterns) {
                if (pattern.test(content)) {
                    return false;
                }
            }

            return true;
        } catch (error) {
            console.error('Template validation error:', error);
            return false;
        }
    }

    /**
     * Export templates to JSON
     */
    exportTemplates() {
        const exportData = {
            templates: this.customTemplates,
            metadata: this.templateMetadata,
            exportDate: new Date().toISOString(),
            version: '1.0'
        };
        return JSON.stringify(exportData, null, 2);
    }

    /**
     * Import templates from JSON
     */
    importTemplates(jsonData) {
        try {
            const importData = JSON.parse(jsonData);

            if (!importData.templates) {
                throw new Error('Invalid template data format');
            }

            let imported = 0;
            let skipped = 0;

            Object.keys(importData.templates).forEach(name => {
                if (this.customTemplates[name]) {
                    skipped++;
                } else {
                    const content = importData.templates[name];
                    const metadata = importData.metadata?.[name] || {};

                    if (this.validateTemplate(content)) {
                        this.customTemplates[name] = content;
                        this.templateMetadata[name] = {
                            ...metadata,
                            imported: new Date().toISOString(),
                            isCustom: true
                        };
                        imported++;
                    }
                }
            });

            if (imported > 0) {
                this.saveTemplates();
                this.refreshTemplateDropdown();
            }

            return { imported, skipped };
        } catch (error) {
            throw new Error('Failed to import templates: ' + error.message);
        }
    }

    /**
     * Refresh the template dropdown in the UI
     */
    refreshTemplateDropdown() {
        // Find all template selectors and refresh them
        const selectors = document.querySelectorAll('.Stickara-template-select');
        selectors.forEach(selector => {
            this.populateTemplateSelector(selector);
        });

        // Trigger custom event for other components
        window.dispatchEvent(new CustomEvent('stickaraTemplatesUpdated', {
            detail: { templates: this.getAllTemplates() }
        }));
    }

    /**
     * Populate a template selector with all templates
     */
    populateTemplateSelector(selector) {
        if (!selector) return;

        // Clear existing options except the first one
        while (selector.children.length > 1) {
            selector.removeChild(selector.lastChild);
        }

        const allTemplates = this.getAllTemplates();
        const templatesByCategory = {};

        // Group templates by category
        Object.keys(allTemplates).forEach(name => {
            const template = allTemplates[name];
            const category = template.metadata.category || 'general';

            if (!templatesByCategory[category]) {
                templatesByCategory[category] = [];
            }

            templatesByCategory[category].push({
                name,
                template,
                isRecent: this.recentTemplates.includes(name)
            });
        });

        // Add recent templates section if any
        if (this.recentTemplates.length > 0) {
            const recentGroup = document.createElement('optgroup');
            recentGroup.label = '🕒 Recently Used';

            this.recentTemplates.slice(0, 5).forEach(name => {
                if (allTemplates[name]) {
                    const option = document.createElement('option');
                    option.value = name;
                    option.textContent = name;
                    recentGroup.appendChild(option);
                }
            });

            selector.appendChild(recentGroup);
        }

        // Add templates by category
        Object.keys(templatesByCategory).sort().forEach(category => {
            const categoryLabel = TEMPLATE_CATEGORIES[category] || category;
            const optgroup = document.createElement('optgroup');
            optgroup.label = categoryLabel;

            templatesByCategory[category]
                .sort((a, b) => a.name.localeCompare(b.name))
                .forEach(({ name, template }) => {
                    const option = document.createElement('option');
                    option.value = name;
                    option.textContent = name;

                    if (template.isCustom) {
                        option.style.fontStyle = 'italic';
                    }

                    optgroup.appendChild(option);
                });

            selector.appendChild(optgroup);
        });
    }

    /**
     * Track template usage
     */
    trackTemplateUsage(name) {
        if (this.templateMetadata[name]) {
            this.templateMetadata[name].usage = (this.templateMetadata[name].usage || 0) + 1;
            this.templateMetadata[name].lastUsed = new Date().toISOString();
        }

        this.addToRecent(name);
        this.saveTemplates();
    }
}

// Create global instance
window.StickaraTemplateManager = new StickaraTemplateManager();

// Make functions available globally
window.templateManager = window.StickaraTemplateManager;

// --- END OF FILE content-template-manager.js ---
