{"manifest_version": 3, "name": "<PERSON><PERSON>", "version": "1.9", "description": "Leave Stick<PERSON>s on any webpage, create flashcards, and record voice notes. Now with optional Google Drive sync and screenshot capture.", "permissions": ["storage", "unlimitedStorage", "activeTab", "tabs", "alarms", "notifications", "downloads", "offscreen", "contextMenus", "identity"], "host_permissions": ["*://*.googleapis.com/*", "*://*.google.com/oauth2/*"], "author": {"email": "[Your Email]"}, "oauth2": {"client_id": "367714630164-dt1eskp3va1dsnvg2475um4l870u7n0t.apps.googleusercontent.com", "scopes": ["https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/calendar.events", "https://www.googleapis.com/auth/documents", "https://www.googleapis.com/auth/drive.file"]}, "key": "fdmjjldcldimnobeondffekkfdgmlhcp", "action": {"default_popup": "popup.html", "default_icon": {"16": "icon16.png", "32": "icon32.png", "48": "icon48.png", "128": "icon128.png"}}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https://* file://*; connect-src 'self' https://*.googleapis.com https://*"}, "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["*://*/*"], "js": ["katex.min.js", "settings.js", "lib/purify.min.js", "lib/dompurify-loader.js", "lib/sanitization-manager.js", "lib/secure-storage.js", "lib/subresource-integrity.js", "lib/permission-manager.js", "lib/secure-messaging.js", "lib/threat-detection.js", "lib/inline-workers.js", "lib/worker-manager.js", "lib/google-docs-integration.js", "content/content-state.js", "content/content-security-utils.js", "content/content-csp.js", "content/content-utils.js", "content/content-templates.js", "content/content-template-manager.js", "content/content-smart-templates.js", "content/content-table-builder.js", "content/content-visual-template-builder.js", "content/content-template-editor.js", "content/content-toolbar.js", "content/content-screenshot.js", "content/content-ui.js", "content/content-notebook-sidebar.js", "content/content-storage.js", "content/content-interactions.js", "content/content-search.js", "content/content-highlighting.js", "content/content-flashcards.js", "content/content-voice.js", "content/content-voice-enhanced.js", "content/content-diagram.js", "content/content-equation-editor.js", "content/content-page-info.js", "content/content-messaging.js", "content/content-quick-snippet.js", "content/content-ui-customization.js", "ui/dashboard-events.js", "ui/google-docs-export.js", "ui/dashboard-gdocs-integration.js", "ui/direct-gdocs-button.js", "content/content-gdocs-button.js", "content/content-rendering-optimization.js", "content/content-main.js"], "css": ["katex.min.css", "content.css", "ui/google-docs-export.css"], "run_at": "document_end"}], "icons": {"16": "icon16.png", "32": "icon32.png", "48": "icon48.png", "128": "icon128.png"}, "web_accessible_resources": [{"resources": ["icon128.png", "fonts/*", "workers/storage-worker.js", "workers/image-worker.js", "workers/data-worker.js", "privacy-policy.html", "privacy-policy.css", "permissions-explanation.html", "permissions-explanation.css", "data-deletion.html", "data-deletion.css", "help.html", "help.css", "lib/google-docs-integration.js", "ui/dashboard-events.js", "ui/google-docs-export.js", "ui/dashboard-gdocs-integration.js", "ui/direct-gdocs-button.js", "content/content-gdocs-button.js", "ui/google-docs-export.css", "standalone-gdocs-button.js"], "matches": ["*://*/*"]}]}