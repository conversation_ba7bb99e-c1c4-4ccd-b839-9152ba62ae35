/**
 * Google Docs Integration Module for Stickara
 * 
 * This module provides functionality to export notes and highlights to Google Docs.
 */

// Create a namespace to avoid global pollution
window.StickaraGoogleDocsIntegration = (function() {
    // Constants
    const DOCS_API_ENDPOINT = 'https://docs.googleapis.com/v1/documents';
    const DRIVE_API_ENDPOINT = 'https://www.googleapis.com/drive/v3';
    
    // Private variables
    let isInitialized = false;
    let authToken = null;
    
    /**
     * Initializes the Google Docs integration
     * @returns {Promise<boolean>} A promise that resolves to true if initialization was successful
     */
    async function init() {
        try {
            // Check if already initialized
            if (isInitialized) {
                return true;
            }
            
            // Get auth token from background script
            const response = await chrome.runtime.sendMessage({
                action: 'getAuthToken',
                scopes: ['https://www.googleapis.com/auth/documents', 'https://www.googleapis.com/auth/drive.file']
            });
            
            if (!response || !response.success || !response.token) {
                console.error('Failed to get auth token for Google Docs integration');
                return false;
            }
            
            authToken = response.token;
            isInitialized = true;
            return true;
        } catch (error) {
            console.error('Error initializing Google Docs integration:', error);
            return false;
        }
    }
    
    /**
     * Creates a new Google Doc
     * @param {string} title - The title of the document
     * @returns {Promise<Object>} A promise that resolves to the created document
     */
    async function createDoc(title) {
        try {
            if (!isInitialized) {
                await init();
            }
            
            const response = await fetch(`${DRIVE_API_ENDPOINT}/files`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: title,
                    mimeType: 'application/vnd.google-apps.document'
                })
            });
            
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Failed to create Google Doc: ${response.status} ${errorText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('Error creating Google Doc:', error);
            throw error;
        }
    }
    
    /**
     * Appends content to a Google Doc
     * @param {string} docId - The ID of the document
     * @param {Array} requests - The requests to append content
     * @returns {Promise<Object>} A promise that resolves to the updated document
     */
    async function batchUpdate(docId, requests) {
        try {
            if (!isInitialized) {
                await init();
            }
            
            const response = await fetch(`${DOCS_API_ENDPOINT}/${docId}:batchUpdate`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    requests: requests
                })
            });
            
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Failed to update Google Doc: ${response.status} ${errorText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('Error updating Google Doc:', error);
            throw error;
        }
    }
    
    /**
     * Formats a note for Google Docs
     * @param {Object} note - The note object
     * @param {number} index - The index of the note
     * @returns {Array} An array of requests for the Google Docs API
     */
    function formatNote(note, index) {
        const requests = [];
        const startIndex = 1; // Document starts at index 1
        
        // Add note title
        const title = note.title || `Note ${index + 1}`;
        requests.push({
            insertText: {
                location: {
                    index: startIndex
                },
                text: title + '\\n'
            }
        });
        
        // Format title as heading
        requests.push({
            updateParagraphStyle: {
                range: {
                    startIndex: startIndex,
                    endIndex: startIndex + title.length
                },
                paragraphStyle: {
                    namedStyleType: 'HEADING_2',
                    spaceAbove: {
                        magnitude: 10,
                        unit: 'PT'
                    },
                    spaceBelow: {
                        magnitude: 5,
                        unit: 'PT'
                    }
                },
                fields: 'namedStyleType,spaceAbove,spaceBelow'
            }
        });
        
        // Add URL if available
        if (note.url) {
            requests.push({
                insertText: {
                    location: {
                        index: startIndex + title.length + 1
                    },
                    text: 'Source: ' + note.url + '\\n\\n'
                }
            });
            
            // Format URL as link
            requests.push({
                updateTextStyle: {
                    range: {
                        startIndex: startIndex + title.length + 9, // "Source: " is 8 chars + 1 newline
                        endIndex: startIndex + title.length + 9 + note.url.length
                    },
                    textStyle: {
                        link: {
                            url: note.url
                        },
                        foregroundColor: {
                            color: {
                                rgbColor: {
                                    blue: 0.8,
                                    red: 0.2,
                                    green: 0.4
                                }
                            }
                        },
                        underline: true
                    },
                    fields: 'link,foregroundColor,underline'
                }
            });
        }
        
        // Add note content
        let contentText = '';
        if (note.text) {
            // Convert HTML to plain text
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = note.text;
            contentText = tempDiv.textContent || tempDiv.innerText || '';
        }
        
        const contentStartIndex = startIndex + title.length + 1 + (note.url ? ('Source: ' + note.url + '\\n\\n').length : 0);
        
        requests.push({
            insertText: {
                location: {
                    index: contentStartIndex
                },
                text: contentText + '\\n\\n'
            }
        });
        
        // Add tags if available
        if (note.tags && note.tags.length > 0) {
            const tagsText = 'Tags: ' + note.tags.join(', ') + '\\n\\n';
            requests.push({
                insertText: {
                    location: {
                        index: contentStartIndex + contentText.length + 2 // +2 for the newlines
                    },
                    text: tagsText
                }
            });
            
            // Format tags
            requests.push({
                updateTextStyle: {
                    range: {
                        startIndex: contentStartIndex + contentText.length + 2,
                        endIndex: contentStartIndex + contentText.length + 2 + 5 // "Tags: " is 5 chars
                    },
                    textStyle: {
                        bold: true
                    },
                    fields: 'bold'
                }
            });
        }
        
        // Add separator
        requests.push({
            insertText: {
                location: {
                    index: contentStartIndex + contentText.length + 2 + (note.tags && note.tags.length > 0 ? ('Tags: ' + note.tags.join(', ') + '\\n\\n').length : 0)
                },
                text: '-----------------------------------\\n\\n'
            }
        });
        
        return requests;
    }
    
    /**
     * Formats a highlight for Google Docs
     * @param {Object} highlight - The highlight object
     * @param {number} index - The index of the highlight
     * @returns {Array} An array of requests for the Google Docs API
     */
    function formatHighlight(highlight, index) {
        const requests = [];
        const startIndex = 1; // Document starts at index 1
        
        // Add highlight source
        if (highlight.url) {
            requests.push({
                insertText: {
                    location: {
                        index: startIndex
                    },
                    text: 'Source: ' + highlight.url + '\\n\\n'
                }
            });
            
            // Format URL as link
            requests.push({
                updateTextStyle: {
                    range: {
                        startIndex: startIndex + 8, // "Source: " is 8 chars
                        endIndex: startIndex + 8 + highlight.url.length
                    },
                    textStyle: {
                        link: {
                            url: highlight.url
                        },
                        foregroundColor: {
                            color: {
                                rgbColor: {
                                    blue: 0.8,
                                    red: 0.2,
                                    green: 0.4
                                }
                            }
                        },
                        underline: true
                    },
                    fields: 'link,foregroundColor,underline'
                }
            });
        }
        
        // Add highlight text
        const highlightStartIndex = startIndex + (highlight.url ? ('Source: ' + highlight.url + '\\n\\n').length : 0);
        
        requests.push({
            insertText: {
                location: {
                    index: highlightStartIndex
                },
                text: highlight.text + '\\n'
            }
        });
        
        // Format highlight text
        requests.push({
            updateTextStyle: {
                range: {
                    startIndex: highlightStartIndex,
                    endIndex: highlightStartIndex + highlight.text.length
                },
                textStyle: {
                    backgroundColor: {
                        color: {
                            rgbColor: {
                                red: 1.0,
                                green: 0.95,
                                blue: 0.8
                            }
                        }
                    }
                },
                fields: 'backgroundColor'
            }
        });
        
        // Add note if available
        if (highlight.note) {
            requests.push({
                insertText: {
                    location: {
                        index: highlightStartIndex + highlight.text.length + 1 // +1 for the newline
                    },
                    text: 'Note: ' + highlight.note + '\\n\\n'
                }
            });
            
            // Format note
            requests.push({
                updateTextStyle: {
                    range: {
                        startIndex: highlightStartIndex + highlight.text.length + 1,
                        endIndex: highlightStartIndex + highlight.text.length + 1 + 5 // "Note: " is 5 chars
                    },
                    textStyle: {
                        bold: true
                    },
                    fields: 'bold'
                }
            });
            
            requests.push({
                updateTextStyle: {
                    range: {
                        startIndex: highlightStartIndex + highlight.text.length + 1 + 5,
                        endIndex: highlightStartIndex + highlight.text.length + 1 + 5 + highlight.note.length
                    },
                    textStyle: {
                        italic: true
                    },
                    fields: 'italic'
                }
            });
        } else {
            // Add extra newline if no note
            requests.push({
                insertText: {
                    location: {
                        index: highlightStartIndex + highlight.text.length + 1
                    },
                    text: '\\n'
                }
            });
        }
        
        // Add separator
        requests.push({
            insertText: {
                location: {
                    index: highlightStartIndex + highlight.text.length + 1 + (highlight.note ? ('Note: ' + highlight.note + '\\n\\n').length : '\\n'.length)
                },
                text: '-----------------------------------\\n\\n'
            }
        });
        
        return requests;
    }
    
    /**
     * Exports notes to a Google Doc
     * @param {Array} notes - The notes to export
     * @param {string} [title='Stickara Notes'] - The title of the document
     * @returns {Promise<Object>} A promise that resolves to the created document
     */
    async function exportNotes(notes, title = 'Stickara Notes') {
        try {
            if (!isInitialized) {
                await init();
            }
            
            // Create a new document
            const doc = await createDoc(title);
            
            // Format document title
            const requests = [{
                updateDocumentStyle: {
                    documentStyle: {
                        marginTop: {
                            magnitude: 36,
                            unit: 'PT'
                        },
                        marginBottom: {
                            magnitude: 36,
                            unit: 'PT'
                        },
                        marginLeft: {
                            magnitude: 36,
                            unit: 'PT'
                        },
                        marginRight: {
                            magnitude: 36,
                            unit: 'PT'
                        }
                    },
                    fields: 'marginTop,marginBottom,marginLeft,marginRight'
                }
            }];
            
            // Add header
            requests.push({
                insertText: {
                    location: {
                        index: 1
                    },
                    text: title + '\\n\\n'
                }
            });
            
            // Format header
            requests.push({
                updateParagraphStyle: {
                    range: {
                        startIndex: 1,
                        endIndex: 1 + title.length
                    },
                    paragraphStyle: {
                        namedStyleType: 'HEADING_1',
                        alignment: 'CENTER'
                    },
                    fields: 'namedStyleType,alignment'
                }
            });
            
            // Format header text
            requests.push({
                updateTextStyle: {
                    range: {
                        startIndex: 1,
                        endIndex: 1 + title.length
                    },
                    textStyle: {
                        foregroundColor: {
                            color: {
                                rgbColor: {
                                    red: 0.2,
                                    green: 0.8,
                                    blue: 0.6
                                }
                            }
                        },
                        fontSize: {
                            magnitude: 18,
                            unit: 'PT'
                        },
                        bold: true
                    },
                    fields: 'foregroundColor,fontSize,bold'
                }
            });
            
            // Add date
            const date = new Date().toLocaleDateString();
            requests.push({
                insertText: {
                    location: {
                        index: 1 + title.length + 2 // +2 for the newlines
                    },
                    text: 'Exported on: ' + date + '\\n\\n'
                }
            });
            
            // Format date
            requests.push({
                updateTextStyle: {
                    range: {
                        startIndex: 1 + title.length + 2,
                        endIndex: 1 + title.length + 2 + 'Exported on: '.length
                    },
                    textStyle: {
                        bold: true
                    },
                    fields: 'bold'
                }
            });
            
            // Add notes
            let currentIndex = 1 + title.length + 2 + 'Exported on: '.length + date.length + 2; // +2 for the newlines
            
            for (let i = 0; i < notes.length; i++) {
                const noteRequests = formatNote(notes[i], i);
                
                // Update indices for each request
                noteRequests.forEach(request => {
                    if (request.insertText && request.insertText.location) {
                        request.insertText.location.index = currentIndex;
                        currentIndex += request.insertText.text.length;
                    } else if (request.updateParagraphStyle && request.updateParagraphStyle.range) {
                        request.updateParagraphStyle.range.startIndex += currentIndex - 1;
                        request.updateParagraphStyle.range.endIndex += currentIndex - 1;
                    } else if (request.updateTextStyle && request.updateTextStyle.range) {
                        request.updateTextStyle.range.startIndex += currentIndex - 1;
                        request.updateTextStyle.range.endIndex += currentIndex - 1;
                    }
                });
                
                requests.push(...noteRequests);
            }
            
            // Update the document
            await batchUpdate(doc.id, requests);
            
            return {
                success: true,
                documentId: doc.id,
                documentUrl: `https://docs.google.com/document/d/${doc.id}/edit`,
                message: 'Notes exported successfully'
            };
        } catch (error) {
            console.error('Error exporting notes to Google Docs:', error);
            return {
                success: false,
                error: error.message || 'Unknown error'
            };
        }
    }
    
    /**
     * Exports highlights to a Google Doc
     * @param {Array} highlights - The highlights to export
     * @param {string} [title='Stickara Highlights'] - The title of the document
     * @returns {Promise<Object>} A promise that resolves to the created document
     */
    async function exportHighlights(highlights, title = 'Stickara Highlights') {
        try {
            if (!isInitialized) {
                await init();
            }
            
            // Create a new document
            const doc = await createDoc(title);
            
            // Format document title
            const requests = [{
                updateDocumentStyle: {
                    documentStyle: {
                        marginTop: {
                            magnitude: 36,
                            unit: 'PT'
                        },
                        marginBottom: {
                            magnitude: 36,
                            unit: 'PT'
                        },
                        marginLeft: {
                            magnitude: 36,
                            unit: 'PT'
                        },
                        marginRight: {
                            magnitude: 36,
                            unit: 'PT'
                        }
                    },
                    fields: 'marginTop,marginBottom,marginLeft,marginRight'
                }
            }];
            
            // Add header
            requests.push({
                insertText: {
                    location: {
                        index: 1
                    },
                    text: title + '\\n\\n'
                }
            });
            
            // Format header
            requests.push({
                updateParagraphStyle: {
                    range: {
                        startIndex: 1,
                        endIndex: 1 + title.length
                    },
                    paragraphStyle: {
                        namedStyleType: 'HEADING_1',
                        alignment: 'CENTER'
                    },
                    fields: 'namedStyleType,alignment'
                }
            });
            
            // Format header text
            requests.push({
                updateTextStyle: {
                    range: {
                        startIndex: 1,
                        endIndex: 1 + title.length
                    },
                    textStyle: {
                        foregroundColor: {
                            color: {
                                rgbColor: {
                                    red: 0.2,
                                    green: 0.8,
                                    blue: 0.6
                                }
                            }
                        },
                        fontSize: {
                            magnitude: 18,
                            unit: 'PT'
                        },
                        bold: true
                    },
                    fields: 'foregroundColor,fontSize,bold'
                }
            });
            
            // Add date
            const date = new Date().toLocaleDateString();
            requests.push({
                insertText: {
                    location: {
                        index: 1 + title.length + 2 // +2 for the newlines
                    },
                    text: 'Exported on: ' + date + '\\n\\n'
                }
            });
            
            // Format date
            requests.push({
                updateTextStyle: {
                    range: {
                        startIndex: 1 + title.length + 2,
                        endIndex: 1 + title.length + 2 + 'Exported on: '.length
                    },
                    textStyle: {
                        bold: true
                    },
                    fields: 'bold'
                }
            });
            
            // Add highlights
            let currentIndex = 1 + title.length + 2 + 'Exported on: '.length + date.length + 2; // +2 for the newlines
            
            for (let i = 0; i < highlights.length; i++) {
                const highlightRequests = formatHighlight(highlights[i], i);
                
                // Update indices for each request
                highlightRequests.forEach(request => {
                    if (request.insertText && request.insertText.location) {
                        request.insertText.location.index = currentIndex;
                        currentIndex += request.insertText.text.length;
                    } else if (request.updateParagraphStyle && request.updateParagraphStyle.range) {
                        request.updateParagraphStyle.range.startIndex += currentIndex - 1;
                        request.updateParagraphStyle.range.endIndex += currentIndex - 1;
                    } else if (request.updateTextStyle && request.updateTextStyle.range) {
                        request.updateTextStyle.range.startIndex += currentIndex - 1;
                        request.updateTextStyle.range.endIndex += currentIndex - 1;
                    }
                });
                
                requests.push(...highlightRequests);
            }
            
            // Update the document
            await batchUpdate(doc.id, requests);
            
            return {
                success: true,
                documentId: doc.id,
                documentUrl: `https://docs.google.com/document/d/${doc.id}/edit`,
                message: 'Highlights exported successfully'
            };
        } catch (error) {
            console.error('Error exporting highlights to Google Docs:', error);
            return {
                success: false,
                error: error.message || 'Unknown error'
            };
        }
    }
    
    // Return the public API
    return {
        init,
        exportNotes,
        exportHighlights,
        isInitialized: () => isInitialized
    };
})();

console.log("Stickara: Google Docs Integration Module Loaded");
